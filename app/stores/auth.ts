import { jwtDecode } from 'jwt-decode'
import { UserType } from '~/types/index.d'
import type { RoleBasedUser } from '~/types/role-based-user'

export const useAuthStore = defineStore('authStore', {
  persist: [
    {
      pick: ['token', 'refresh_token', 'user'],
      storage: window?.sessionStorage
    }
  ],
  state: () => ({
    openUserMenu: false,
    isChangePasswordModalOpen: false,
    token: '' as string | null,
    refresh_token: '' as string | null,
    user: null as RoleBasedUser | null,
    loadings: {} as Record<string, any>,
    errors: {} as Record<string, any>,
    challenge: {} as any,
    userAccessLogs: [] as any[],
    lockedIpAddresses: [] as any[],
    lockedUsers: [] as any[],
    userAccessLogPagination: {
      page: 1,
      pageCount: 10,
      asc: false
    },
    userAccessLogFilter: {} as Record<string, any>,
    usersAccessLogTotalCount: 0,
    temp: {
      username: null,
      tenant_id: null
    } as Record<string, any>,
    // for demo data
    originalUserAccessLogs: null as any,
    accessibleEndpoints: [] as any[]
  }),
  getters: {
    authenticated: state => !!state.token,
    userRole(state) {
      return state.user?.role
    },
    isOperator(state) {
      return state.user?.role === UserType.PNL_ADMIN
    },
    isAdmin(state) {
      return state.user?.role === UserType.ADMIN
    },
    isStaff(state) {
      return state.user?.role === UserType.STAFF
    },
    isGuest(state) {
      return state.user?.role === UserType.GUEST
    },
    userType(state): UserType {
      return (state.user?.role as UserType) || UserType.GUEST
    }
  },
  actions: {
    async login(tenant_id: string, username: string, password: string) {
      try {
        this.loadings.login = true
        this.errors.login = null
        const response = await useAPI().authService.post(
          '/login',
          {
            tenant_id: tenant_id ? tenant_id : undefined,
            username,
            password
          },
          {}
        )
        if (response.data?.challenge?.name === 'NEW_PASSWORD_REQUIRED') {
          this.challenge = response.data?.challenge
          this.temp.username = username
          this.temp.tenant_id = tenant_id
          navigateTo('/auth/new-password')
          return false
        } else {
          this.token = response.data?.token
          this.refresh_token = response.data?.refresh_token

          // Decode the token to get user information
          if (this.token) {
            const decodedUser = jwtDecode(this.token) as RoleBasedUser
            this.user = decodedUser

            // Set the selected tenant ID based on the user's tenant_id
            // This ensures non-operator users have their tenant selected automatically
            if (
              decodedUser.tenant_id
              && decodedUser.role !== UserType.PNL_ADMIN
            ) {
              const { selectedTenantId } = useApp()
              selectedTenantId.value = decodedUser.tenant_id

              // const ragsStore = useRagsStore()
              // ragsStore.embedChatbotForCurrentTenantAndEnv(
              //   this.refresh_token || ''
              // )
            }
          }

          return response.data
        }
      } catch (error: any) {
        console.log('Login error response:', error?.response?.data)
        this.errors.login = error?.response?.data || error
        return false
      } finally {
        this.loadings.login = false
      }
    },
    async initPassword(password: string) {
      try {
        this.loadings.initPassword = true
        this.errors.initPassword = null
        const { tenant_id, username } = this.temp
        const response = await useAPI().authService.post(
          '/login/challenge',
          {
            tenant_id: tenant_id ? tenant_id : undefined,
            username,
            password,
            challenge: this.challenge
          },
          {}
        )
        this.token = response.data?.token
        this.refresh_token = response.data?.refresh_token

        // Decode the token to get user information
        if (this.token) {
          const decodedUser = jwtDecode(this.token) as RoleBasedUser
          this.user = decodedUser

          // Set the selected tenant ID based on the user's tenant_id
          // This ensures non-operator users have their tenant selected automatically
          if (
            decodedUser.tenant_id
            && decodedUser.role !== UserType.PNL_ADMIN
          ) {
            const { selectedTenantId } = useApp()
            selectedTenantId.value = decodedUser.tenant_id
          }
        }

        return response.data
      } catch (error: any) {
        this.errors.initPassword = error?.response?.data || error
        return false
      } finally {
        this.loadings.initPassword = false
      }
    },
    async forgetPassword(username: string): Promise<boolean> {
      try {
        this.errors.forgetPassword = null
        this.loadings.forgetPassword = true
        await useAPI().authService.post(
          '/forget',
          {
            username
          },
          {}
        )
        this.temp.username = username
        return true
      } catch (error: any) {
        this.errors.forgetPassword = error?.response?.data || error
        return false
      } finally {
        this.loadings.forgetPassword = false
      }
    },
    async confirmPassword(password: string, confirm_code: string) {
      try {
        this.loadings.confirmPassword = true
        this.errors.confirmPassword = null
        const username = this.temp.username
        await useAPI().authService.post(
          '/forget/confirm',
          {
            username,
            password,
            confirm_code
          },
          {}
        )
      } catch (error: any) {
        this.errors.confirmPassword = error?.response?.data || error
      } finally {
        this.loadings.confirmPassword = false
      }
    },
    async changePassword(currentPassword: string, newPassword: string) {
      try {
        this.loadings.changePassword = true
        this.errors.changePassword = null
        await useAPI().adminService.put(
          '/v2/users/password',
          {
            old_password: currentPassword,
            new_password: newPassword
          },
          {}
        )
        return true
      } catch (error: any) {
        this.errors.changePassword = error?.response?.data || error
        return false
      } finally {
        this.loadings.changePassword = false
      }
    },
    async logout() {
      const isOperator = this.isOperator
      this.token = null
      this.refresh_token = null
      this.user = null
      // Clear permissions when logging out
      const permissionsStore = usePermissionsStore()
      const ragsStore = useRagsStore()

      ragsStore?.llmRagChatbot?.value?.logout()
      ragsStore?.llmRagChatbot?.value?.hideBubble()
      ragsStore.tenantTokens = {}

      permissionsStore.clearPermissions()
      if (isOperator) {
        navigateTo('/auth/operator/login')
      } else {
        navigateTo('/auth/login')
      }
    },
    removeChatbotBubble() {
      const script = document.getElementById('chatbot-script')
      if (script) {
        script.remove()
      }
      // remove id="llm-rag-chatbot"
      const chatbot = document.getElementById('llm-rag-chatbot')
      if (chatbot) {
        chatbot.remove()
      }
    },
    async fetchUser() {
      try {
        this.loadings.fetchUser = true
        this.errors.fetchUser = null
        await useAPI().authService.get('/me')
        if (this.token) {
          const decodedUser = jwtDecode(this.token) as RoleBasedUser
          this.user = decodedUser

          // Initialize permissions after user is fetched
          // const permissionsStore = usePermissionsStore()
          // permissionsStore.setUserPermissions(this.user?.role as UserType)

          // // Also initialize app permissions
          // useAppPermissions()

          // Set the selected tenant ID based on the user's tenant_id
          // This ensures non-operator users have their tenant selected automatically
          if (
            decodedUser.tenant_id
            && decodedUser.role !== UserType.PNL_ADMIN
          ) {
            const { selectedTenantId } = useApp()
            selectedTenantId.value = decodedUser.tenant_id
          }
        }
      } catch (error: any) {
        this.errors.fetchUser = error?.response?.data || error
        this.user = null
        navigateTo('/auth/login')
      } finally {
        this.loadings.fetchUser = false
      }
    },
    async refreshToken() {
      try {
        this.loadings.refreshToken = true
        this.errors.refreshToken = null
        const response = await useAPI().authService.post('/login/refresh', {})
        this.token = response.data?.token
        this.refresh_token = response.data?.refresh_token
        return response.data
      } catch (error: any) {
        this.errors.refreshToken = error?.response?.data || error
        return false
      } finally {
        this.loadings.refreshToken = false
      }
    },
    async getUserAccessLogs(tenant_id: string) {
      try {
        this.loadings.getAccessLogs = true
        this.userAccessLogs = []
        const response = await useAPI().authService.get('/login/all', {
          params: {
            tenant_id,
            page: this.userAccessLogPagination.page,
            page_size: this.userAccessLogPagination.pageCount,
            order: this.userAccessLogPagination.asc,
            username_exact: this.userAccessLogFilter.username || null,
            count_lock: this.userAccessLogFilter.count_lock?.value ?? null,
            ip_address: this.userAccessLogFilter.ip_address || null
          },
          paramsSerializer: {
            indexes: null
          }
        })
        const pagination = tryParseJson(response?.headers['x-pagination'])
        this.usersAccessLogTotalCount = pagination?.total_count
        this.userAccessLogs = response.data.logs
        return true
      } catch (error: any) {
        this.errors.getAccessLogs = error?.response?.data || error
        return false
      } finally {
        this.loadings.getAccessLogs = false
      }
    },
    async getLockedIpAddresses(ip_address?: string) {
      try {
        this.loadings.getLockedIps = true
        this.lockedIpAddresses = []
        const response = await useAPI().authService.get('/locked/ip/all', {
          params: {
            ip_address: ip_address || null
          }
        })
        this.lockedIpAddresses = response.data.ip_addresses
        return true
      } catch (error: any) {
        this.errors.getLockedIps = error?.response?.data || error
        return false
      } finally {
        this.loadings.getLockedIps = false
      }
    },
    async getLockedUsers(username?: string) {
      try {
        this.loadings.getLockedUsers = true
        this.lockedUsers = []
        const response = await useAPI().authService.get('/locked/users/all', {
          params: {
            username: username || null
          },
          paramsSerializer: {
            indexes: null
          }
        })
        this.lockedUsers = response.data.usernames
        return true
      } catch (error: any) {
        this.errors.getLockedUsers = error?.response?.data || error
        return false
      } finally {
        this.loadings.getLockedUsers = false
      }
    },
    async releaseUserLock(username: string) {
      try {
        this.loadings.releaseUserLock = true
        await useAPI().authService.post(
          '/release/user',
          {
            username
          },
          {}
        )
        return true
      } catch (error: any) {
        this.errors.releaseUserLock = error?.response?.data || error
        return false
      } finally {
        this.loadings.releaseUserLock = false
      }
    },
    async releaseIpLock(ip_address: string) {
      try {
        this.loadings.releaseIpLock = true
        const result = await useAPI().authService.post(
          '/release/ip',
          {
            ip_address
          },
          {}
        )
        if (result.data === null) {
          await this.getLockedIpAddresses()
        }
        return true
      } catch (error: any) {
        this.errors.releaseIpLock = error?.response?.data || error
        return false
      } finally {
        this.loadings.releaseIpLock = false
      }
    },
    async getAllAccessibleEndpoints() {
      try {
        this.loadings.getAllAccessibleEndpoints = true
        this.accessibleEndpoints = []
        const response = await useAPI().authService.get('/endpoints/all', {})
        this.accessibleEndpoints = response.data.endpoints
        return true
      } catch (error: any) {
        this.errors.getAllAccessibleEndpoints = error?.response?.data || error
        return false
      } finally {
        this.loadings.getAllAccessibleEndpoints = false
      }
    },
    addDemoUserAccessLogs() {
      this.originalUserAccessLogs = {
        userAccessLogs: this.userAccessLogs,
        usersAccessLogTotalCount: this.usersAccessLogTotalCount
      }
      this.userAccessLogs = [
        {
          username: 'demo-user-1',
          tenant_id: 'demo-tenant-1',
          result: true,
          ip_address: '*******',
          user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) Safari/XX.XX',
          id: 'XXXX',
          target_api: 'admin',
          count_lock: false,
          release_username: null,
          created_at: '2020-06-12T16:23:44.720250+09:00',
          release_at: null,
          isDemo: true
        },
        {
          username: 'demo-user-1',
          tenant_id: 'demo-tenant-1',
          result: true,
          ip_address: '*******',
          user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_1) Safari/XX.XX',
          id: 'XXXX',
          target_api: 'admin',
          count_lock: false,
          release_username: null,
          created_at: '2020-06-05T11:27:50.182185+09:00',
          release_at: null,
          isDemo: true
        },
        {
          username: 'demo-user-1',
          tenant_id: 'demo-tenant-1',
          result: true,
          ip_address: '*******',
          user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_1) Safari/XX.XX',
          id: 'XXXX',
          target_api: 'admin',
          count_lock: true,
          release_username: null,
          created_at: '2020-06-05T11:27:50.182185+09:00',
          release_at: null,
          isDemo: true
        }
      ]
      this.usersAccessLogTotalCount = 3
    },
    removeDemoUserAccessLogs() {
      if (this.originalUserAccessLogs) {
        this.userAccessLogs = this.originalUserAccessLogs.userAccessLogs
        this.usersAccessLogTotalCount = this.originalUserAccessLogs.usersAccessLogTotalCount
      } else {
        this.userAccessLogs = this.userAccessLogs.filter((item: any) => !item.isDemo)
      }
    }
  }
})
