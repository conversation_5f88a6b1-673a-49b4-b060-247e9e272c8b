import type {
  Tenant,
  TenantWithEnvironments,
  CreateTenantPayload,
  UpdateTenantPayload,
  TenantListResponse
} from '~/types/tenant'

import { UserType } from '~/types/index.d'

export const useTenantsStore = defineStore('tenantsStore', {
  persist: {
    pick: [''],
    storage: window?.localStorage
  },
  state: () => ({
    allTenants: [] as Tenant[], // New state for all tenants used in TenantsSelect
    tenants: [] as Tenant[],
    selectedTenantId: useRoute().params.tenantId as string, // init from route.params.tenantId
    loadings: {} as Record<string, any>,
    errors: {} as Record<string, any>,
    showTenantsPalette: false,
    tenantsPagination: {
      page: 1,
      pageCount: 10,
      asc: false
    },
    tenantsTotal: 0,
    tenantsFilter: {} as Record<string, any>,
    selectedTenants: [] as Tenant[]
  }),
  getters: {
    // New getter for all tenants used in TenantsSelect
    allTenantsDropdownItems(): Array<
      Tenant & {
        id: string
        label: string
        click: () => void
        avatar?: any
      }
    > {
      return this.allTenants.map(tenant => ({
        ...tenant,
        label: tenant.description || tenant.id,
        avatar_url: tenant.environments?.[0]?.custom?.settings?.avatar_url,
        click: () => {
          this.selectedTenantId = tenant.id
          this.showTenantsPalette = false
          // reload the page with new tenant id
          window.location.href = `/${tenant.id}`
        }
      }))
    },
    tenantsWithEnvironments(): TenantWithEnvironments[] {
      return this.tenants.map(tenant => ({
        ...tenant,
        environments:
          tenant.environments?.sort((a: any, b: any) => a.environment - b.environment)
          || [],
        avatar: {
          src: tenant.environments?.[0]?.custom?.settings?.avatar_url
        }
      }))
    },

    tenantsDropdownItems(): Array<
      TenantWithEnvironments & {
        slot: string
        label: string
        click: () => void
        avatar?: any
      }
    > {
      return this.tenantsWithEnvironments.map(tenant => ({
        ...tenant,
        slot: 'tenant' + tenant.id,
        label: tenant.description || tenant.id,
        click: () => {
          this.selectedTenantId = tenant.id
          // reload the page with new tenant id
          window.location.href = `/${tenant.id}`
        }
      }))
    },

    topFiveTenantsDropdownItems(): Array<any> {
      if (this.tenantsDropdownItems.length <= 5) {
        return this.tenantsDropdownItems
      }
      const remainAvatars = this.tenantsDropdownItems
        .slice(5, this.tenantsDropdownItems.length)
        .map(tenant => tenant.avatar)

      // Use type assertion to avoid TypeScript errors with concat
      const moreItem = {
        slot: 'more',
        label: 'もっと見る',
        remainAvatars,
        remainCount: this.tenantsDropdownItems.length - 5,
        click: () => {
          this.showTenantsPalette = true
        }
      }

      return [...this.tenantsDropdownItems.slice(0, 5), moreItem as any]
    },

    firstTenant():
      | (TenantWithEnvironments & {
        slot: string
        label: string
        click: () => void
        avatar?: any
      })
      | undefined {
      return this.tenantsDropdownItems?.[0]
    },

    selectedTenant():
      | (TenantWithEnvironments & {
        slot: string
        label: string
        click: () => void
        avatar?: any
      })
      | undefined {
      return (
        this.tenantsDropdownItems.find(
          tenant => tenant.id === this.selectedTenantId
        ) || this.firstTenant
      )
    },

    selectedTenantAnotherSlot():
      | (TenantWithEnvironments & {
        slot: string
        label: string
        click: () => void
        avatar?: any
      })
      | undefined {
      // Find another tenant with the same tenant_id (if applicable)
      // Note: tenant_id is not in our interface, so we're using a type assertion
      return this.tenantsDropdownItems.find(
        tenant =>
          tenant.id !== this.selectedTenant?.id
          && (tenant as any).tenant_id === (this.selectedTenant as any)?.tenant_id
      )
    }
  },
  actions: {
    resetFilters() {
      this.tenantsFilter = {}
    },
    /**
     * Fetch tenants based on user role with pagination support
     * - For operators (PNL_ADMIN): Fetch all tenants using /v2/tenants/all
     * - For non-operators (Admin, Staff): Fetch their assigned tenant using /v2/tenants/{tenant_id}
     */
    async fetchTenants() {
      try {
        this.loadings.fetchTenants = true
        this.errors.fetchTenants = null

        const authStore = useAuthStore()
        const { userRole } = storeToRefs(authStore)
        console.log('🚀 ~ fetchTenants ~ userRole:', userRole)

        // Use role-based API calls for both operators and non-operators
        const roleBasedApiCalls = useRoleBasedApiCalls()

        // For operators, add pagination parameters
        if (authStore.isOperator) {
          const params = {
            page: this.tenantsPagination.page,
            page_size: this.tenantsPagination.pageCount,
            order: this.tenantsPagination.asc
          } as Record<string, any>

          if (this.tenantsFilter?.description) {
            params.description = this.tenantsFilter.description
          }
          if (this.tenantsFilter?.id) {
            params.id = this.tenantsFilter.id
          }

          const response = await useAPI().adminService.get(
            '/v2/tenants/all/details',
            {
              params
            }
          )
          const pagination = tryParseJson(response?.headers['x-pagination'])
          this.tenantsTotal = pagination?.total_count || 0
          this.tenants = response.data?.tenants || []
        } else {
          // For non-operators, use the existing role-based API call
          const response
            = await roleBasedApiCalls.tenants.fetch<TenantListResponse>()
          this.tenants = response?.tenants || []
          this.tenantsTotal = this.tenants.length
        }

        if (!this.selectedTenantId) {
          this.selectedTenantId = this.tenants[0]?.id || ''
        }
        return true
      } catch (error: any) {
        this.errors.fetchTenants = error?.response?.data || error
        return false
      } finally {
        this.loadings.fetchTenants = false
      }
    },
    async createTenant(payload: CreateTenantPayload) {
      try {
        this.loadings.createTenant = true
        this.errors.createTenant = null

        const roleBasedApiCalls = useRoleBasedApiCalls()
        const response = await roleBasedApiCalls.tenants.create(payload)

        this.tenants.push(response)
        return true
      } catch (error: any) {
        this.errors.createTenant = error?.response?.data || error
        return false
      } finally {
        this.loadings.createTenant = false
      }
    },
    async updateTenant(id: string, payload: UpdateTenantPayload) {
      try {
        this.loadings.updateTenant = true
        this.errors.updateTenant = null

        const roleBasedApiCalls = useRoleBasedApiCalls()
        const response = await roleBasedApiCalls.tenants.update(id, payload)

        this.tenants = this.tenants.map((tenant) => {
          if (tenant.id === response.id) {
            return response
          }
          return tenant
        })
        return true
      } catch (error: any) {
        this.errors.updateTenant = error?.response?.data || error
        return false
      } finally {
        this.loadings.updateTenant = false
      }
    },
    async deleteTenant(id: string) {
      try {
        this.loadings.deleteTenant = true
        this.errors.deleteTenant = null

        const roleBasedApiCalls = useRoleBasedApiCalls()
        await roleBasedApiCalls.tenants.delete(id)

        this.tenants = this.tenants.filter(tenant => tenant.id !== id)
        return true
      } catch (error: any) {
        this.errors.deleteTenant = error?.response?.data || error
        return false
      } finally {
        this.loadings.deleteTenant = false
      }
    },

    async fetchAllTenants() {
      try {
        this.loadings.fetchAllTenants = true
        this.errors.fetchAllTenants = null

        const authStore = useAuthStore()

        // For operators, use the all tenants endpoint without pagination
        if (authStore.isOperator) {
          const response = await useAPI().adminService.get('/v2/tenants/all/details', {
            params: { page_size: 1000 } // Get a large number to fetch all
          })
          this.allTenants = response.data?.tenants || []
        } else {
          // For non-operators, use the role-based API call
          const roleBasedApiCalls = useRoleBasedApiCalls()
          const response
            = await roleBasedApiCalls.tenants.fetch<TenantListResponse>()
          this.allTenants = response?.tenants || []
        }

        return true
      } catch (error: any) {
        this.errors.fetchAllTenants = error?.response?.data || error
        return false
      } finally {
        this.loadings.fetchAllTenants = false
      }
    }
  }
})
