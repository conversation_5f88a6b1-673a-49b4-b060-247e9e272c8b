export interface UsageData {
  from_date: string
  to_date: string
  tenant_id: string
  usage: {
    azure_search: {
      total: number
      usage: Array<{
        endpoint: string
        index_name: string
        count: number
      }>
    }
    batch: {
      total: number
      usage: Array<{
        chat_model_name: string
        embedding_model_name: string
        token_count: number
        prompt_tokens: number
        completion_tokens: number
      }>
    }
    chat: {
      total: number
      usage: Array<{
        chat_model_name: string
        embedding_model_name: string
        token_count: number
        prompt_tokens: number
        completion_tokens: number
      }>
    }
    google_search: {
      total: number
    }
    knowledge: {
      total: number
      usage: Array<{
        storage_account_name: string
        container_name: string
        documents_count: number
        contents_count: number
      }>
    }
  }
}

export interface UsageSearchFilters {
  from_date?: string
  to_date?: string
  env_id?: string
  environment?: number | null
  request_id?: string
  session_id?: string
}

export const EnvironmentType = {
  PRODUCTION: 1,
  STAGING: 2
} as const

export type EnvironmentTypeValue = typeof EnvironmentType[keyof typeof EnvironmentType]

export interface TenantUsageData {
  tenantId: string
  tenantName: string
  data: UsageData | null
  filters: UsageSearchFilters
  loading: boolean
  error: any
}

export const useUsageStore = defineStore('usageStore', {
  persist: {
    pick: ['tenantUsageList'],
    storage: window?.localStorage
  },
  state: () => ({
    // Legacy single tenant data (keep for backward compatibility)
    usageData: null as UsageData | null,
    selectedTenantId: '' as string,
    searchFilters: {
      from_date: '',
      to_date: '',
      env_id: '',
      environment: null,
      request_id: '',
      session_id: ''
    } as UsageSearchFilters,
    loadings: {} as Record<string, any>,
    errors: {} as Record<string, any>,

    // Multi-tenant comparison data
    tenantUsageList: [] as TenantUsageData[],
    activeTab: 'comparison' as string,
    comparisonFilters: {
      from_date: '',
      to_date: '',
      env_id: '',
      environment: null,
      request_id: '',
      session_id: ''
    } as UsageSearchFilters
  }),
  getters: {
    // Get total usage statistics
    totalUsageStats: (state) => {
      if (!state.usageData) return null

      return {
        azureSearchTotal: state.usageData.usage.azure_search.total,
        batchTotal: state.usageData.usage.batch.total,
        chatTotal: state.usageData.usage.chat.total,
        googleSearchTotal: state.usageData.usage.google_search.total,
        knowledgeTotal: state.usageData.usage.knowledge.total
      }
    },

    // Get detailed usage breakdown
    detailedUsage: (state) => {
      if (!state.usageData) return null

      return {
        azureSearch: state.usageData.usage.azure_search.usage,
        batch: state.usageData.usage.batch.usage,
        chat: state.usageData.usage.chat.usage,
        knowledge: state.usageData.usage.knowledge.usage
      }
    },

    // Get date range
    dateRange: (state) => {
      if (!state.usageData) return null

      return {
        fromDate: state.usageData.from_date,
        toDate: state.usageData.to_date
      }
    },

    // Multi-tenant comparison getters
    comparisonData: (state) => {
      return state.tenantUsageList.filter(tenant => tenant.data !== null)
    },

    comparisonChartData: (state) => {
      const tenants = state.tenantUsageList.filter(tenant => tenant.data !== null)

      return {
        labels: tenants.map(tenant => tenant.tenantName),
        datasets: [
          {
            label: 'Azure Search',
            data: tenants.map(tenant => tenant.data?.usage.azure_search.total || 0),
            backgroundColor: 'rgba(59, 130, 246, 0.5)',
            borderColor: 'rgb(59, 130, 246)',
            borderWidth: 1
          },
          {
            label: 'Chat',
            data: tenants.map(tenant => tenant.data?.usage.chat.total || 0),
            backgroundColor: 'rgba(147, 51, 234, 0.5)',
            borderColor: 'rgb(147, 51, 234)',
            borderWidth: 1
          },
          {
            label: 'Batch',
            data: tenants.map(tenant => tenant.data?.usage.batch.total || 0),
            backgroundColor: 'rgba(34, 197, 94, 0.5)',
            borderColor: 'rgb(34, 197, 94)',
            borderWidth: 1
          },
          {
            label: 'Google Search',
            data: tenants.map(tenant => tenant.data?.usage.google_search.total || 0),
            backgroundColor: 'rgba(249, 115, 22, 0.5)',
            borderColor: 'rgb(249, 115, 22)',
            borderWidth: 1
          },
          {
            label: 'Knowledge',
            data: tenants.map(tenant => tenant.data?.usage.knowledge.total || 0),
            backgroundColor: 'rgba(99, 102, 241, 0.5)',
            borderColor: 'rgb(99, 102, 241)',
            borderWidth: 1
          }
        ]
      }
    },

    comparisonSummary: (state) => {
      const tenants = state.tenantUsageList.filter(tenant => tenant.data !== null)

      if (tenants.length === 0) return null

      const summary = {
        totalTenants: tenants.length,
        totalAzureSearch: 0,
        totalChat: 0,
        totalBatch: 0,
        totalGoogleSearch: 0,
        totalKnowledge: 0,
        averageAzureSearch: 0,
        averageChat: 0,
        averageBatch: 0,
        averageGoogleSearch: 0,
        averageKnowledge: 0
      }

      tenants.forEach(tenant => {
        if (tenant.data) {
          summary.totalAzureSearch += tenant.data.usage.azure_search.total
          summary.totalChat += tenant.data.usage.chat.total
          summary.totalBatch += tenant.data.usage.batch.total
          summary.totalGoogleSearch += tenant.data.usage.google_search.total
          summary.totalKnowledge += tenant.data.usage.knowledge.total
        }
      })

      summary.averageAzureSearch = Math.round(summary.totalAzureSearch / tenants.length)
      summary.averageChat = Math.round(summary.totalChat / tenants.length)
      summary.averageBatch = Math.round(summary.totalBatch / tenants.length)
      summary.averageGoogleSearch = Math.round(summary.totalGoogleSearch / tenants.length)
      summary.averageKnowledge = Math.round(summary.totalKnowledge / tenants.length)

      return summary
    }
  },
  actions: {
    /**
     * Fetch tenant usage data
     * GET /v2/usage/tenants/{tenant_id}
     */
    async fetchTenantUsage(tenantId: string, filters?: UsageSearchFilters) {
      try {
        this.loadings.fetchTenantUsage = true
        this.errors.fetchTenantUsage = null
        this.selectedTenantId = tenantId

        // Build query parameters
        const params: Record<string, any> = {}

        if (filters?.from_date) {
          params.from_date = filters.from_date
        }
        if (filters?.to_date) {
          params.to_date = filters.to_date
        }
        if (filters?.env_id) {
          params.env_id = filters.env_id
        }
        if (filters?.environment !== null && filters?.environment !== undefined) {
          params.environment = filters.environment
        }
        if (filters?.request_id) {
          params.request_id = filters.request_id
        }
        if (filters?.session_id) {
          params.session_id = filters.session_id
        }

        const response = await useAPI().adminService.get(
          `/v2/usage/tenants/${tenantId}`,
          { params }
        )

        this.usageData = response.data
        return response.data
      } catch (error: any) {
        this.errors.fetchTenantUsage = error?.response?.data || error
        return false
      } finally {
        this.loadings.fetchTenantUsage = false
      }
    },

    /**
     * Update search filters
     */
    updateSearchFilters(filters: Partial<UsageSearchFilters>) {
      this.searchFilters = { ...this.searchFilters, ...filters }
    },

    /**
     * Reset search filters to default values
     */
    resetSearchFilters() {
      this.searchFilters = {
        from_date: '',
        to_date: '',
        env_id: '',
        environment: null,
        request_id: '',
        session_id: ''
      }
    },

    /**
     * Add tenant to comparison list
     */
    async addTenantToComparison(tenantId: string, tenantName: string, filters?: UsageSearchFilters) {
      // Check if tenant already exists
      const existingIndex = this.tenantUsageList.findIndex(t => t.tenantId === tenantId)

      if (existingIndex !== -1) {
        // Update existing tenant
        this.tenantUsageList[existingIndex].loading = true
        this.tenantUsageList[existingIndex].error = null
        this.tenantUsageList[existingIndex].filters = filters || {}
      } else {
        // Add new tenant
        this.tenantUsageList.push({
          tenantId,
          tenantName,
          data: null,
          filters: filters || {},
          loading: true,
          error: null
        })
      }

      try {
        const response = await this.fetchTenantUsage(tenantId, filters)
        const tenantIndex = this.tenantUsageList.findIndex(t => t.tenantId === tenantId)

        if (tenantIndex !== -1) {
          this.tenantUsageList[tenantIndex].data = response
          this.tenantUsageList[tenantIndex].loading = false
        }

        return response
      } catch (error: any) {
        const tenantIndex = this.tenantUsageList.findIndex(t => t.tenantId === tenantId)
        if (tenantIndex !== -1) {
          this.tenantUsageList[tenantIndex].error = error
          this.tenantUsageList[tenantIndex].loading = false
        }
        throw error
      }
    },

    /**
     * Remove tenant from comparison
     */
    removeTenantFromComparison(tenantId: string) {
      const index = this.tenantUsageList.findIndex(t => t.tenantId === tenantId)
      if (index !== -1) {
        this.tenantUsageList.splice(index, 1)
      }
    },

    /**
     * Update comparison filters and refresh all tenants
     */
    async updateComparisonFilters(filters: UsageSearchFilters) {
      this.comparisonFilters = { ...filters }

      // Refresh all tenants with new filters
      const promises = this.tenantUsageList.map(tenant =>
        this.addTenantToComparison(tenant.tenantId, tenant.tenantName, filters)
      )

      await Promise.allSettled(promises)
    },

    /**
     * Set active tab
     */
    setActiveTab(tab: string) {
      this.activeTab = tab
    },

    /**
     * Clear all comparison data
     */
    clearComparisonData() {
      this.tenantUsageList = []
      this.activeTab = 'comparison'
    },

    /**
     * Export comparison data to CSV
     */
    exportComparisonToCSV() {
      const tenants = this.tenantUsageList.filter(tenant => tenant.data !== null)

      if (tenants.length === 0) {
        throw new Error('比較データがありません')
      }

      // CSV headers
      const headers = [
        'Tenant ID',
        'Tenant Name',
        'From Date',
        'To Date',
        'Azure Search Total',
        'Chat Total',
        'Batch Total',
        'Google Search Total',
        'Knowledge Total'
      ]

      // CSV rows
      const rows = tenants.map(tenant => [
        tenant.tenantId,
        tenant.tenantName,
        tenant.data?.from_date || '',
        tenant.data?.to_date || '',
        tenant.data?.usage.azure_search.total || 0,
        tenant.data?.usage.chat.total || 0,
        tenant.data?.usage.batch.total || 0,
        tenant.data?.usage.google_search.total || 0,
        tenant.data?.usage.knowledge.total || 0
      ])

      // Create CSV content
      const csvContent = [headers, ...rows]
        .map(row => row.map(field => `"${field}"`).join(','))
        .join('\n')

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)

      link.setAttribute('href', url)
      link.setAttribute('download', `usage-comparison-${new Date().toISOString().split('T')[0]}.csv`)
      link.style.visibility = 'hidden'

      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },

    /**
     * Clear usage data (legacy)
     */
    clearUsageData() {
      this.usageData = null
      this.selectedTenantId = ''
      this.errors = {}
    }
  }
})
