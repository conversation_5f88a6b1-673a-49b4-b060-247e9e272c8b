import { orderBy } from 'lodash'

export const useCategoriesStore = defineStore('categoriesStore', {
  persist: {
    pick: [''],
    storage: window?.localStorage
  },
  state: () => ({
    categories: [] as any[],
    loadings: {} as Record<string, any>,
    categoryForm: false,
    errors: {} as Record<string, any>,
    showCategoriesPalette: false,
    originalCategories: null as any
  }),
  getters: {
    categoriesForDropDown(): any[] {
      return this.categories?.map(item => ({
        label: item.category,
        value: item.category_id
      })) || []
    }
  },
  actions: {
    async fetchCategories(tenant_id: string, env_id: string, force = false) {
      // check if categories are already fetched and tenant_id and env_id are same
      if (this.categories.length > 0 && !force) {
        if (
          this.categories.every(
            row => row.tenant_id === tenant_id && row.env_id === env_id
          )
        ) {
          return this.categories
        }
      }
      try {
        this.loadings.fetchCategories = true
        this.errors.fetchCategories = null
        this.categories = []
        const response = await useAPI().adminService.get(
          `/v2/stat/category/all/tenants/${tenant_id}/env/${env_id}`
        )
        this.categories = orderBy(
          response.data?.categories || [],
          ['updated_at'],
          ['desc']
        )
        return response.data?.categories
      } catch (error: any) {
        this.errors.fetchCategories = error?.response?.data || error
        return false
      } finally {
        this.loadings.fetchCategories = false
      }
    },
    async createCategory(tenant_id: string, env_id: string, payload: any) {
      try {
        this.loadings.createCategory = true
        this.errors = {}
        const response = await useAPI().adminService.post(
          `/v2/stat/category/tenants/${tenant_id}/env/${env_id}`,
          payload
        )
        this.categories.unshift(response.data)
        return response.data
      } catch (error: any) {
        this.errors.createCategory = error?.response?.data || error
        return false
      } finally {
        this.loadings.createCategory = false
      }
    },
    async updateCategory(tenant_id: string, env_id: string, payload: any) {
      try {
        this.loadings.updateCategory = true
        this.errors = {}
        const response = await useAPI().adminService.put(
          `/v2/stat/category/${payload?.category_id}/tenants/${tenant_id}/env/${env_id}`,
          {
            category: payload.category
          }
        )
        this.categories = this.categories.map((row) => {
          if (row.category_id === response.data.category_id) {
            return response.data
          }
          return row
        })
        return true
      } catch (error: any) {
        this.errors.updateCategory = error?.response?.data || error
        return false
      } finally {
        this.loadings.updateCategory = false
      }
    },
    async deleteCategory(
      tenant_id: string,
      env_id: string,
      category_id: number
    ) {
      try {
        this.loadings.deleteCategory = true
        this.errors.deleteCategory = null
        await useAPI().adminService.delete(
          `/v2/stat/category/${category_id}/tenants/${tenant_id}/env/${env_id}`
        )
        this.categories = this.categories.filter(
          row => row.category_id !== category_id
        )
        return true
      } catch (error: any) {
        this.errors.deleteCategory = error?.response?.data || error
        return false
      } finally {
        this.loadings.deleteCategory = false
      }
    },
    addCategoriesDemoData() {
      this.originalCategories = {
        categories: this.categories
      }
      this.categories = [
        {
          category: '地方',
          env_id: 'demo',
          tenant_id: 'demo',
          category_id: 1,
          created_username: 'demo_user',
          updated_username: 'demo_user',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          isDemo: true
        },
        {
          category: '都会',
          env_id: 'demo',
          tenant_id: 'demo',
          category_id: 2,
          created_username: 'demo_user',
          updated_username: 'demo_user',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          isDemo: true
        },
        {
          category: '海',
          env_id: 'demo',
          tenant_id: 'demo',
          category_id: 3,
          created_username: 'demo_user',
          updated_username: 'demo_user',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          isDemo: true
        },
        {
          category: '山',
          env_id: 'demo',
          tenant_id: 'demo',
          category_id: 4,
          created_username: 'demo_user',
          updated_username: 'demo_user',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          isDemo: true
        },
        {
          category: '駐車場',
          env_id: 'demo',
          tenant_id: 'demo',
          category_id: 5,
          created_username: 'demo_user',
          updated_username: 'demo_user',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          isDemo: true
        },
        {
          category: '保険',
          env_id: 'demo',
          tenant_id: 'demo',
          category_id: 6,
          created_username: 'demo_user',
          updated_username: 'demo_user',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          isDemo: true
        },
        {
          category: '安全訓練',
          env_id: 'demo',
          tenant_id: 'demo',
          category_id: 7,
          created_username: 'demo_user',
          updated_username: 'demo_user',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          isDemo: true
        },
        {
          category: '学校',
          env_id: 'demo',
          tenant_id: 'demo',
          category_id: 8,
          created_username: 'demo_user',
          updated_username: 'demo_user',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          isDemo: true
        }
      ]
    },
    removeCategoriesDemoData() {
      if (this.originalCategories) {
        this.categories = this.originalCategories.categories
      } else {
        this.categories = this.categories.filter((item: any) => !item.isDemo)
      }
    }
  }
})
