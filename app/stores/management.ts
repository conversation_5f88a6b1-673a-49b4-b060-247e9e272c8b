import { mkConfig, generateCsv } from 'export-to-csv'
import { saveAs } from 'file-saver'
import { tryParseJson } from '~/utils'

export const useManagementStore = defineStore('managementStore', {
  state: () => ({
    apiActionLogs: [] as any[],
    apiActionLogsFilterKeyword: '',
    selectedApiActionLog: null,
    apiActionLogsPagination: {
      page: 1,
      pageCount: 10,
      asc: false
    },
    apiActionLogsPageTotal: 0,
    apiActionLogsTotalCount: 0,
    loadings: {} as Record<string, any>,
    errors: {} as Record<string, any>
  }),
  getters: {
    apiActionLogsFiltered: (state) => {
      if (!state.apiActionLogsFilterKeyword) {
        return state.apiActionLogs
      }

      const keyword = state.apiActionLogsFilterKeyword.toLowerCase()
      return state.apiActionLogs.filter((log) => {
        return (
          (log.user_id && log.user_id.toLowerCase().includes(keyword))
          || (log.created_username && log.created_username.toLowerCase().includes(keyword))
          || (log.action && log.action.toLowerCase().includes(keyword))
          || (log.table_name && log.table_name.toLowerCase().includes(keyword))
          || (log.endpoint && log.endpoint.toLowerCase().includes(keyword))
          || (log.tenant_id && log.tenant_id.toLowerCase().includes(keyword))
          || (log.env_id && log.env_id.toString().toLowerCase().includes(keyword))
        )
      })
    }
  },
  actions: {
    async fetchApiActionLogs() {
      const { selectedTenantId, selectedEnvId } = useApp()
      try {
        this.loadings.fetchApiActionLogs = true
        this.errors.fetchApiActionLogs = null
        this.apiActionLogs = []
        this.apiActionLogsPageTotal = 0
        this.apiActionLogsTotalCount = 0

        // Use role-based API calls
        const roleBasedApiCalls = useRoleBasedApiCalls()

        // Define the parameters
        const params = {
          page: this.apiActionLogsPagination.page,
          page_size: this.apiActionLogsPagination.pageCount,
          order: this.apiActionLogsPagination.asc,
          tenant_id: selectedTenantId.value,
          env_id: selectedEnvId.value
        }

        // Define the endpoints for different roles - only operators can access this
        const response = await roleBasedApiCalls.custom(
          {
            operator: '/v2/management/histories/all',
            admin: '', // Not accessible to admin
            staff: '', // Not accessible to staff
            default: '',
            params: {},
            query: params
          },
          'get'
        )

        this.apiActionLogs = response?.histories || []

        // Debug: Log the response to understand the structure
        console.log('API Action Logs Response:', response)

        // Get pagination info from response data (added by role-based API)
        let pagination = null
        if (response?.pagination) {
          // If pagination is already parsed
          pagination = typeof response.pagination === 'string'
            ? tryParseJson(response.pagination)
            : response.pagination
        } else if (response?.headers?.['x-pagination']) {
          // Fallback to headers if available
          pagination = tryParseJson(response.headers['x-pagination'])
        }

        console.log('Parsed pagination:', pagination)

        this.apiActionLogsPageTotal = pagination?.total_pages || 0
        this.apiActionLogsTotalCount = pagination?.total_count || 0

        console.log('Final pagination values:', {
          pageTotal: this.apiActionLogsPageTotal,
          totalCount: this.apiActionLogsTotalCount
        })

        return true
      } catch (error: any) {
        this.errors.fetchApiActionLogs = error?.response?.data || error
        return false
      } finally {
        this.loadings.fetchApiActionLogs = false
      }
    },
    exportApiActionLogsToCSV(rows: any[], documentName?: string) {
      // Transform the data to flatten complex objects for CSV export
      const transformedRows = rows.map(row => {
        const transformedRow: any = {}

        // Copy simple fields
        Object.keys(row).forEach(key => {
          const value = row[key]

          // Handle complex objects by converting to JSON string
          if (key === 'original_data' || key === 'new_data') {
            if (value && typeof value === 'object') {
              transformedRow[key] = JSON.stringify(value)
            } else if (typeof value === 'string') {
              // If it's already a string, keep it as is
              transformedRow[key] = value
            } else {
              transformedRow[key] = value || ''
            }
          } else if (value && typeof value === 'object' && !Array.isArray(value)) {
            // Convert other objects to JSON string
            transformedRow[key] = JSON.stringify(value)
          } else if (Array.isArray(value)) {
            // Convert arrays to semicolon-separated string
            transformedRow[key] = value.join(';')
          } else {
            // Keep primitive values as is
            transformedRow[key] = value
          }
        })

        // Format the created_at date for better readability in CSV
        if (transformedRow.created_at) {
          transformedRow.created_at = new Date(transformedRow.created_at).toLocaleString()
        }

        return transformedRow
      })

      const config = mkConfig({ useKeysAsHeaders: true })
      const csvOutput = generateCsv(config)(transformedRows)
      const blob = new Blob([csvOutput as unknown as BlobPart], {
        type: 'text/csv;charset=utf-8'
      })
      saveAs(blob, `${documentName || 'api_action_logs'}.csv`)
    },
    async exportAllLogs() {
      const { selectedTenantId, selectedEnvId } = useApp()
      try {
        this.loadings.exportAllLogs = true
        this.errors.exportAllLogs = null

        // Use role-based API calls
        const roleBasedApiCalls = useRoleBasedApiCalls()

        // Define the parameters
        const params = {
          page: 1,
          page_size: 10000,
          order: this.apiActionLogsPagination.asc,
          tenant_id: selectedTenantId.value,
          env_id: selectedEnvId.value
        }

        // Define the endpoints for different roles - only operators can access this
        const response = await roleBasedApiCalls.custom(
          {
            operator: '/v2/management/histories/all',
            admin: '', // Not accessible to admin
            staff: '', // Not accessible to staff
            default: '',
            params: {},
            query: params
          },
          'get'
        )

        const allLogs = response?.histories || []
        this.exportApiActionLogsToCSV(allLogs, 'all_api_action_logs')
        return true
      } catch (error: any) {
        this.errors.exportAllLogs = error?.response?.data || error
        return false
      } finally {
        this.loadings.exportAllLogs = false
      }
    }
  }
})
