import { UserType } from '~/types/index.d'

// refer https://www.notion.so/playnext-lab/1b684d393a9c8021a2efdb9bd5e10136
// Define permission constants
export const PERMISSIONS = {
  // Dashboard permissions
  VIEW_DASHBOARD: 'view_dashboard',

  // Statistics permissions
  VIEW_STATISTICS: 'view_statistics',

  // Log permissions
  VIEW_LOGS: 'view_logs',
  DOWNLOAD_LOGS_CSV: 'download_logs_csv',

  // Connect permissions
  VIEW_CONNECT: 'view_connect',

  // Training data permissions
  VIEW_TRAINING_DATA: 'view_training_data',
  CREATE_TRAINING_DATA: 'create_training_data',
  EDIT_TRAINING_DATA: 'edit_training_data',
  DELETE_TRAINING_DATA: 'delete_training_data',

  // Knowledge permissions
  VIEW_KNOWLEDGE: 'view_knowledge',
  EDIT_KNOWLEDGE: 'edit_knowledge',
  DELETE_KNOWLEDGE: 'delete_knowledge',
  DOWNLOAD_KNOWLEDGE_CSV: 'download_knowledge_csv',

  // Basic settings permissions
  VIEW_BASIC_SETTINGS: 'view_basic_settings',
  EDIT_BASIC_SETTINGS: 'edit_basic_settings',

  // Chatbot settings permissions
  VIEW_CHATBOT_SETTINGS: 'view_chatbot_settings',
  EDIT_CHATBOT_SETTINGS: 'edit_chatbot_settings',

  // Error message settings permissions
  VIEW_ERROR_MESSAGES: 'view_error_messages',
  EDIT_ERROR_MESSAGES: 'edit_error_messages',

  // Survey settings permissions
  VIEW_SURVEY_SETTINGS: 'view_survey_settings',
  EDIT_SURVEY_SETTINGS: 'edit_survey_settings',

  // RAG settings permissions
  VIEW_RAG_SETTINGS: 'view_rag_settings',
  EDIT_RAG_SETTINGS: 'edit_rag_settings',

  // Knowledge settings permissions
  VIEW_KNOWLEDGE_SETTINGS: 'view_knowledge_settings',
  EDIT_KNOWLEDGE_SETTINGS: 'edit_knowledge_settings',

  // Labels permissions
  VIEW_LABELS: 'view_labels',
  CREATE_LABEL: 'create_label',
  EDIT_LABEL: 'edit_label',
  DELETE_LABEL: 'delete_label',

  // Category management permissions
  VIEW_CATEGORIES: 'view_categories',
  CREATE_CATEGORY: 'create_category',
  EDIT_CATEGORY: 'edit_category',
  DELETE_CATEGORY: 'delete_category',

  // Admin settings permissions
  VIEW_ADMIN_SETTINGS: 'view_admin_settings',
  EDIT_ADMIN_SETTINGS: 'edit_admin_settings',

  // Tenant management permissions
  VIEW_TENANTS: 'view_tenants',
  CREATE_TENANT: 'create_tenant',
  EDIT_TENANT: 'edit_tenant',
  DELETE_TENANT: 'delete_tenant',

  // User management permissions
  VIEW_USERS: 'view_users',
  CREATE_USER: 'create_user',
  EDIT_USER: 'edit_user',
  DELETE_USER: 'delete_user',

  // User groups permissions
  VIEW_USER_GROUPS: 'view_user_groups',
  CREATE_USER_GROUP: 'create_user_group',
  EDIT_USER_GROUP: 'edit_user_group',
  DELETE_USER_GROUP: 'delete_user_group',

  // System settings permissions
  VIEW_SYSTEM_SETTINGS: 'view_system_settings',
  EDIT_SYSTEM_SETTINGS: 'edit_system_settings',

  // Deployment permissions
  DEPLOY: 'deploy',
  VIEW_DEPLOY_HISTORY: 'view_deploy_history',

  // Preview permissions
  USE_PREVIEW: 'use_preview',

  // Management permissions (Operator only)
  VIEW_API_ACTION_LOGS: 'view_api_action_logs',

  // User lock release permissions
  RELEASE_USER_LOCK: 'release_user_lock',

  // IP Address Control permissions
  VIEW_IP_ADDRESS_CONTROL: 'view_ip_address_control',
  CREATE_IP_ADDRESS_SETTING: 'create_ip_address_setting',
  EDIT_IP_ADDRESS_SETTING: 'edit_ip_address_setting',
  DELETE_IP_ADDRESS_SETTING: 'delete_ip_address_setting',

  // Prompts management permissions
  VIEW_PROMPTS: 'view_prompts',
  MANAGE_PROMPTS: 'manage_prompts'
}

// Define role-based permissions mapping
export const ROLE_PERMISSIONS = {
  // Staff permissions - based on the permission table
  [UserType.STAFF]: [
    // Common permissions for both production and staging environments
    PERMISSIONS.VIEW_DASHBOARD,
    PERMISSIONS.VIEW_STATISTICS,
    PERMISSIONS.VIEW_LOGS,
    PERMISSIONS.DOWNLOAD_LOGS_CSV,
    PERMISSIONS.USE_PREVIEW,

    // View-only permissions for training data and knowledge in production environment
    PERMISSIONS.VIEW_TRAINING_DATA,
    PERMISSIONS.VIEW_KNOWLEDGE,

    // Additional permissions in staging environment
    // These will be conditionally applied based on environment
    // For now, we include them all and will filter at runtime if needed
    PERMISSIONS.CREATE_TRAINING_DATA,
    PERMISSIONS.EDIT_TRAINING_DATA,
    PERMISSIONS.DELETE_TRAINING_DATA,
    PERMISSIONS.EDIT_KNOWLEDGE,
    PERMISSIONS.DELETE_KNOWLEDGE,
    PERMISSIONS.DOWNLOAD_KNOWLEDGE_CSV
  ],

  // Admin permissions - based on the permission table
  [UserType.ADMIN]: [
    // Common permissions for both production and staging environments
    PERMISSIONS.VIEW_DASHBOARD,
    PERMISSIONS.VIEW_STATISTICS,
    PERMISSIONS.VIEW_LOGS,
    PERMISSIONS.DOWNLOAD_LOGS_CSV,
    PERMISSIONS.USE_PREVIEW,
    PERMISSIONS.VIEW_USERS,
    PERMISSIONS.CREATE_USER,
    PERMISSIONS.EDIT_USER,
    PERMISSIONS.DELETE_USER,
    PERMISSIONS.DEPLOY,
    PERMISSIONS.VIEW_DEPLOY_HISTORY,

    // View-only permissions in commercial environment
    PERMISSIONS.VIEW_TRAINING_DATA,
    PERMISSIONS.VIEW_KNOWLEDGE,
    PERMISSIONS.DOWNLOAD_KNOWLEDGE_CSV,
    PERMISSIONS.VIEW_BASIC_SETTINGS,
    PERMISSIONS.VIEW_CHATBOT_SETTINGS,
    PERMISSIONS.VIEW_ERROR_MESSAGES,
    PERMISSIONS.VIEW_SURVEY_SETTINGS,
    PERMISSIONS.VIEW_LABELS,
    PERMISSIONS.VIEW_CATEGORIES,

    // Additional permissions in testing environment
    // These will be conditionally applied based on environment
    // For now, we include them all and will filter at runtime if needed
    PERMISSIONS.CREATE_TRAINING_DATA,
    PERMISSIONS.EDIT_TRAINING_DATA,
    PERMISSIONS.DELETE_TRAINING_DATA,
    PERMISSIONS.EDIT_KNOWLEDGE,
    PERMISSIONS.DELETE_KNOWLEDGE,
    PERMISSIONS.EDIT_BASIC_SETTINGS,
    PERMISSIONS.EDIT_CHATBOT_SETTINGS,
    PERMISSIONS.EDIT_ERROR_MESSAGES,
    PERMISSIONS.EDIT_SURVEY_SETTINGS,
    PERMISSIONS.EDIT_KNOWLEDGE_SETTINGS,
    PERMISSIONS.CREATE_LABEL,
    PERMISSIONS.EDIT_LABEL,
    PERMISSIONS.DELETE_LABEL,
    PERMISSIONS.CREATE_CATEGORY,
    PERMISSIONS.EDIT_CATEGORY,
    PERMISSIONS.DELETE_CATEGORY,
    PERMISSIONS.VIEW_USER_GROUPS,
    PERMISSIONS.CREATE_USER_GROUP,
    PERMISSIONS.EDIT_USER_GROUP,
    PERMISSIONS.DELETE_USER_GROUP,

    PERMISSIONS.RELEASE_USER_LOCK,

    // IP Address Control permissions
    PERMISSIONS.VIEW_IP_ADDRESS_CONTROL,
    PERMISSIONS.CREATE_IP_ADDRESS_SETTING,
    PERMISSIONS.EDIT_IP_ADDRESS_SETTING,
    PERMISSIONS.DELETE_IP_ADDRESS_SETTING
  ],

  // PNL Admin/Operator permissions - has all permissions
  [UserType.PNL_ADMIN]: [
    // Operator has all permissions in both commercial and testing environments
    ...Object.values(PERMISSIONS),

    // Additional permissions specific to PNL Admin
    PERMISSIONS.VIEW_TENANTS,
    PERMISSIONS.CREATE_TENANT,
    PERMISSIONS.EDIT_TENANT,
    PERMISSIONS.DELETE_TENANT,
    PERMISSIONS.VIEW_ADMIN_SETTINGS,
    PERMISSIONS.EDIT_ADMIN_SETTINGS
  ]
}

export const usePermissionsStore = defineStore('permissionsStore', {
  state: () => ({
    userPermissions: [] as string[],
    environmentType: 'staging' as 'production' | 'staging'
  }),
  getters: {
    hasPermission: state => (permission: string) => {
      return state.userPermissions.includes(permission)
    },
    hasAnyPermission: state => (permissions: string[]) => {
      return permissions.some(permission => state.userPermissions.includes(permission))
    },
    hasAllPermissions: state => (permissions: string[]) => {
      return permissions.every(permission => state.userPermissions.includes(permission))
    },
    isProductionEnvironment: (state) => {
      return state.environmentType === 'production'
    }
  },
  actions: {
    setUserPermissions(userRole: UserType) {
      this.userPermissions = ROLE_PERMISSIONS[userRole as keyof typeof ROLE_PERMISSIONS] || []
    },
    clearPermissions() {
      this.userPermissions = []
    },
    /**
     * Set environment type based on the selected environment
     * If environment is 1, it's production, otherwise it's staging
     */
    setEnvironmentType() {
      const environmentsStore = useEnvironmentsStore()
      const { selectedEnv } = storeToRefs(environmentsStore)

      // If selectedEnv.environment is 1, it's production, otherwise it's staging
      this.environmentType = selectedEnv.value?.environment === 1 ? 'production' : 'staging'
    }
  }
})
