export interface UserAccountBase {
  email: string
  display_name?: string
}

export interface UserAccount extends UserAccountBase {
  tenant_id: string
  username: string
  user_type: string
  enabled: boolean
  created_username: string
  updated_username: string
  created_at: string
  updated_at: string
  last_login_at: string | null
  isDemo?: boolean
}

export interface GetAllUserAccounts {
  users: UserAccount[]
}
