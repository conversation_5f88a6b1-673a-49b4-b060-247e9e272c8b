import type { Avatar } from '#ui/types'

export type UserStatus = 'subscribed' | 'unsubscribed' | 'bounced'

// GUEST: Guest
// STAFF: Staff
// ADMIN: Admin
// PNL_ADMIN: Operator
export enum UserType {
  GUEST = 'guest',
  STAFF = 'staff',
  ADMIN = 'admin',
  PNL_ADMIN = 'operator'
}

export interface User {
  id: number
  name: string
  email: string
  avatar?: Avatar
  status: UserStatus
  location: string
}

export interface Mail {
  id: number
  unread?: boolean
  from: User
  subject: string
  body: string
  date: string
}

export interface Member {
  name: string
  username: string
  role: 'member' | 'owner'
  avatar: Avatar
}

export interface Notification {
  id: number
  unread?: boolean
  sender: User
  body: string
  date: string
}

/**
 * Interface for maintenance activation request payload
 */
export interface MaintenanceActivationPayload {
  /** Maintenance message to display */
  message: string
  /** Start date in YYYY-MM-DD HH:mm:ss format (optional for indefinite maintenance) */
  start_date?: string
  /** End date in YYYY-MM-DD HH:mm:ss format (optional for indefinite maintenance) */
  end_date?: string
}

/**
 * Interface for maintenance status response
 */
export interface MaintenanceStatus {
  /** Maintenance message */
  message: string
  /** Whether maintenance mode is active */
  maintenance: boolean
  /** Start date in YYYY-MM-DD HH:mm:ss format (optional for indefinite maintenance) */
  start_date?: string
  /** End date in YYYY-MM-DD HH:mm:ss format (optional for indefinite maintenance) */
  end_date?: string
  /** Tenant ID */
  tenant_id: string
}

/**
 * Interface for all active maintenance status response
 */
export interface AllActiveMaintenanceResponse {
  /** Array of active maintenance statuses */
  tenants: MaintenanceStatus[]
}

export type Period = 'daily' | 'weekly' | 'monthly'

export interface Range {
  start: Date
  end: Date
}

export interface Chat {
  chat_id: string
  message: string
  type: 'human' | 'ai'
  sending?: boolean
  related_documents?: any[]
  prompt_tokens?: number
  completion_tokens?: number
  token_count?: number
}

export type TourName = string

// Prompt types
export interface Prompt {
  system_message: string
  template: string
  input_variables: string[]
  optional_variables: string[]
  enabled: boolean
  env_id: string
  tenant_id: string
  prompt_type: number
  created_username: string
  updated_username: string
  created_at: string
  updated_at: string
}

export interface CreatePromptPayload {
  system_message: string
  template: string
  input_variables: string[]
  optional_variables: string[]
  enabled: boolean
  prompt_type: number
}

export interface UpdatePromptPayload {
  system_message?: string
  template?: string
  input_variables?: string[]
  optional_variables?: string[]
  enabled?: boolean
}

export interface PromptDeleteResponse {
  error_code: number
  error_message: string
  error_base: {
    template: string
    params: Record<string, any>
  }
}
