<script setup lang="ts">
import type { FormSubmitEvent } from '#ui/types'

const { selectedTenantId, selectedEnvId, isSelectedEnvIsProd } = useApp()
const uiClass = {
  formGroup: 'grid grid-cols-12 gap-2 items-center'
}

const ui = {
  formGroup: {
    container: 'col-span-7',
    inner: 'col-span-5'
  }
}

const route = useRoute()

const { trainingDataNavigators } = useNavigators()

const trainingDatasStore = useTrainingDatasStore()
const {
  trainingDataDetailContent,
  knowledgesCount,
  selectedTrainingData,
  loadings
} = storeToRefs(trainingDatasStore)
const breadcrumb = computed(() => {
  return [
    {
      label: trainingDataNavigators.value[0].label,
      icon: trainingDataNavigators.value[0].icon,
      to: `/${selectedTenantId.value}/${selectedEnvId.value}/training-data/`
    },
    {
      label: selectedTrainingData.value?.name,
      icon: contextTypeIcon(selectedTrainingData.value?.original_context_type)
    },
    {
      label: 'ナレッジ一覧',
      icon: 'carbon:ibm-knowledge-catalog',
      to: `/${selectedTenantId.value}/${selectedEnvId.value}/training-data/${route.params.id}`
    },
    {
      label: shorterFileName(trainingDataDetailContent.value?.blob_path),
      icon: 'si:crosshair-detailed-line'
    }
  ]
})

onMounted(() => {
  if (!route.query.tour) {
    trainingDatasStore.fetchTrainingDataDetailContent(
      route.params.id as string,
      route.params.content_id as string,
      selectedTenantId.value,
      selectedEnvId.value
    )
  }
})

const state = reactive({})

const toast = useToast()
const router = useRouter()
const onCancel = () => {
  router.push(
    `/${selectedTenantId.value}/${selectedEnvId.value}/training-data/${route.params.id}`
  )
}

async function onSubmit() {
  const result = await trainingDatasStore.updateTrainingDataDetail(
    route.params.id as string,
    route.params.content_id as string,
    selectedTenantId.value,
    selectedEnvId.value
  )
  if (result) {
    toast.add({
      id: 'update-training-data-detail',
      title: '保存成功',
      description: 'ナレッジの内容を保存しました。',
      color: 'green'
    })
    onCancel()
  }
}
</script>

<template>
  <UDashboardPage>
    <UDashboardPanel grow>
      <UDashboardNavbar :badge="knowledgesCount">
        <template #title>
          <UBreadcrumb :links="breadcrumb" />
        </template>
      </UDashboardNavbar>
      <UDashboardPanelContent data-tour="knowledgedetail">
        <UForm
          v-if="trainingDataDetailContent"
          class="pb-20"
          :state="state"
          :validate-on="['submit']"
          @submit="onSubmit"
        >
          <UDashboardSection title="ナレッジ詳細">
            <template #description>
              <div class="text-xs text-gray-500 dark:text-gray-400">
                Blobパス：{{ trainingDataDetailContent?.blob_path }}
              </div>
            </template>
            <UFormGroup
              name="content"
              label="ナレッジ内容"
              description="ナレッジの内容を入力してください。"
              required
              :class="uiClass.formGroup"
              class="items-baseline"
              :ui="{
                container: 'col-span-12',
                inner: 'col-span-12'
              }"
            >
              <BaseJsonPropertyEditor
                v-model="trainingDataDetailContent.content"
                :disabled="isSelectedEnvIsProd"
                data-tour="json-editor"
              />
            </UFormGroup>
            <UFormGroup
              name="priority"
              label="優先度"
              data-tour="knowledgedetail-priority"
              description="1~9999の範囲の数字を設定ください。"
              help="数字が小さいほど回答生成の際に優先して使用されます。"
              required
              :class="uiClass.formGroup"
              :ui="ui.formGroup"
            >
              <BasePriorityInput
                v-model="trainingDataDetailContent.priority"
                class="w-fit"
              />
            </UFormGroup>
            <UFormGroup
              v-if="trainingDataDetailContent.label"
              name="label"
              label="ラベル"
              data-tour="knowledgedetail-label"
              description="ラベルを設定することによりデータソースの分類が可能になります。"
              required
              :class="uiClass.formGroup"
              :ui="ui.formGroup"
            >
              <LabelsSelect v-model="trainingDataDetailContent.label" />
            </UFormGroup>
            <UFormGroup
              name="enabled"
              label="ステータス"
              data-tour="knowledgedetail-enabled"
              description="ナレッジの有効/無効を設定してください。"
              required
              :class="uiClass.formGroup"
              :ui="ui.formGroup"
            >
              <UToggle
                v-model="trainingDataDetailContent.enabled"
                size="md"
              />
            </UFormGroup>

            <UFormGroup
              label=" "
              :class="uiClass.formGroup"
              :ui="ui.formGroup"
            >
              <div class="flex gap-4">
                <UButton
                  label="キャンセル"
                  color="gray"
                  @click="onCancel"
                />
                <UButton
                  :disabled="isSelectedEnvIsProd"
                  class="min-w-[100px] justify-center"
                  label="保存"
                  color="primary"
                  type="submit"
                  :loading="loadings.updateTrainingDataDetail"
                />
              </div>
            </UFormGroup>
          </UDashboardSection>
        </UForm>
      </UDashboardPanelContent>
    </UDashboardPanel>
  </UDashboardPage>
</template>
