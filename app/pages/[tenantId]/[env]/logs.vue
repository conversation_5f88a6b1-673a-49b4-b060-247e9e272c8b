<script setup lang="ts">
import { sub } from 'date-fns'
import type BaseDateRangePicker from '~/components/base/BaseDateRangePicker.vue'
import type { Mail } from '~/types'
import { debounce } from 'lodash'

const route = useRoute()
const { selectedTenantId, selectedEnv, selectedEnvId } = useApp()
const logsStore = useLogsStore()
const {
  logsFilter,
  logsSubFilter,
  selectedLog,
  logsFilterKeyword,
  logsFiltered,
  logsFilteredUniqueBySessionId,
  allRelatedLogs,
  loadings,
  logPagination,
  logPaginationTotal,
  showAdvancedSearch
} = storeToRefs(logsStore)
const categoryStore = useCategoriesStore()
const { categoriesForDropDown } = storeToRefs(categoryStore)
onMounted(async () => {
  await categoryStore.fetchCategories(
    selectedTenantId.value,
    selectedEnvId.value
  )
})

const selectedMode = ref(0)

const messageSelectedLogs = ref<any[]>([])
const sessionSelectedLogs = ref<any[]>([])

const selectedMail = ref<Mail | null>()

const isMailPanelOpen = computed({
  get() {
    return !!selectedMail.value
  },
  set(value: boolean) {
    if (!value) {
      selectedMail.value = null
    }
  }
})
const searchLogsWithKeyword = debounce(() => {
  if (logPagination.value.page === 1) {
    logsStore.searchLogs(
      selectedTenantId.value,
      selectedEnv.value?.environment || 0
    )
  } else {
    logPagination.value.page = 1
  }
}, 1000)

watch(
  () => logsFilter.value,
  () => {
    // Don't load real data during tour mode
    if (!route.query.tour) {
      logsStore.searchLogs(
        selectedTenantId.value,
        selectedEnv.value?.environment || 0
      )
    }
    // Only clear selected logs when filter changes, not when pagination changes
    messageSelectedLogs.value = []
    sessionSelectedLogs.value = []
  },
  {
    deep: true,
    immediate: true
  }
)
watch(
  () => logsFilterKeyword.value,
  () => {
    searchLogsWithKeyword()
  }
)
watch(
  () => logPagination.value.pageCount,
  () => {
    logPagination.value.page = 1
  }
)

watch(
  () => logPagination.value,
  () => {
    if (!route.query.tour) {
      logsStore.searchLogs(
        selectedTenantId.value,
        selectedEnv.value?.environment || 0
      )
    }
    // Remove clearing of selected logs when pagination changes
    // This allows keeping selected items across pages
  },
  { deep: true, immediate: true }
)
const showLogsFilterKeyword = ref(true)
const keywordFilterRef = ref<any | null>(null)
watch(
  () => showLogsFilterKeyword.value,
  () => {
    if (showLogsFilterKeyword.value) {
      nextTick(() => {
        keywordFilterRef.value?.input?.focus()
      })
    }
  }
)

defineShortcuts({
  '/': () => {
    keywordFilterRef.value?.input?.focus()
  }
})

const tabItems = [
  {
    label: 'メッセージ単位'
  },
  {
    label: 'セッション単位'
  }
]

const logs = computed(() => {
  if (selectedMode.value === 0) {
    return logsFiltered.value
  } else {
    return logsFilteredUniqueBySessionId.value
  }
})
const exportSelectedLogsToCSV = (rows: any) => {
  if (selectedMode.value === 0) {
    logsStore.exportLogsToCSV([rows], rows.query, categoriesForDropDown.value)
  } else {
    const filteredLog = allRelatedLogs.value
      .filter(log => log.session_id === rows.session_id)
      .sort(
        (a, b) =>
          new Date(a.query_created_at).getTime()
            - new Date(b.query_created_at).getTime()
      )
    logsStore.exportLogsToCSV(
      filteredLog,
      `session_unit_logs`,
      categoriesForDropDown.value
    )
  }
}

const currentSelectedLogs = computed({
  get() {
    return selectedMode.value === 0
      ? messageSelectedLogs.value
      : sessionSelectedLogs.value
  },
  set(newValue) {
    if (selectedMode.value === 0) {
      messageSelectedLogs.value = newValue
    } else {
      sessionSelectedLogs.value = newValue
    }
  }
})

// Check if all items in current page are selected
const isAllCurrentPageSelected = computed(() => {
  if (logs.value.length === 0) return false

  return logs.value.every((log) => {
    if (selectedMode.value === 0) {
      return messageSelectedLogs.value.some(
        selected => selected.request_id === log.request_id
      )
    } else {
      return sessionSelectedLogs.value.some(
        selected => selected.session_id === log.session_id
      )
    }
  })
})

// Check if some items in current page are selected (for indeterminate state)
const isSomeCurrentPageSelected = computed(() => {
  if (logs.value.length === 0) return false

  return logs.value.some((log) => {
    if (selectedMode.value === 0) {
      return messageSelectedLogs.value.some(
        selected => selected.request_id === log.request_id
      )
    } else {
      return sessionSelectedLogs.value.some(
        selected => selected.session_id === log.session_id
      )
    }
  })
})

// Handle select all checkbox
const handleSelectAll = (checked: boolean) => {
  if (checked) {
    // Add all current page items to selection
    if (selectedMode.value === 0) {
      // Message mode: add individual messages
      const currentLogs = messageSelectedLogs.value
      const newItems = logs.value.filter(
        log =>
          !currentLogs.some(
            selected => selected.request_id === log.request_id
          )
      )
      messageSelectedLogs.value = [...messageSelectedLogs.value, ...newItems]
    } else {
      // Session mode: add all logs for each session on current page
      const currentLogs = sessionSelectedLogs.value
      const newSessionItems: any[] = []

      logs.value.forEach((log) => {
        if (
          !currentLogs.some(
            selected => selected.session_id === log.session_id
          )
        ) {
          // Find all logs with the same session_id from logsFiltered
          const logsWithSameSession = logsFiltered.value.filter(
            l => l.session_id === log.session_id
          )
          newSessionItems.push(...logsWithSameSession)
        }
      })

      sessionSelectedLogs.value = [
        ...sessionSelectedLogs.value,
        ...newSessionItems
      ]
    }
  } else {
    // Remove all current page items from selection
    if (selectedMode.value === 0) {
      messageSelectedLogs.value = messageSelectedLogs.value.filter(
        selected =>
          !logs.value.some(log => log.request_id === selected.request_id)
      )
    } else {
      sessionSelectedLogs.value = sessionSelectedLogs.value.filter(
        selected =>
          !logs.value.some(log => log.session_id === selected.session_id)
      )
    }
  }
}

// Computed property for selected logs count
const selectedLogsCount = computed(() => {
  if (selectedMode.value === 0) {
    return messageSelectedLogs.value.length
  } else {
    // For session mode, count actual logs that will be exported
    // Get unique session IDs to avoid counting duplicates
    const uniqueSessionIds = new Set(
      sessionSelectedLogs.value.map(log => log.session_id)
    )
    return logsFiltered.value.filter(log =>
      uniqueSessionIds.has(String(log.session_id))
    ).length
  }
})

// Computed property for selected sessions count (for session mode display)
const selectedSessionsCount = computed(() => {
  if (selectedMode.value !== 1) return 0

  // Get unique session IDs to avoid counting duplicates
  const uniqueSessionIds = new Set(
    sessionSelectedLogs.value.map(log => log.session_id)
  )
  return uniqueSessionIds.size
})

// Computed property for chip display text
const chipDisplayText = computed(() => {
  if (selectedLogsCount.value === 0) return ''

  if (selectedMode.value === 0) {
    return selectedLogsCount.value.toString()
  } else {
    // For session mode, show sessions count with 'S' suffix to indicate sessions
    return `${selectedSessionsCount.value}S`
  }
})

const rowMenus = () => {
  const currentSelectedLogs
    = selectedMode.value === 0
      ? messageSelectedLogs.value
      : sessionSelectedLogs.value
  const menus = [
    [
      {
        // Export all logs
        label: `すべてのログを出力 (${logPaginationTotal.value}件)`,
        icon: 'ph:file-csv-light',
        click: async () => {
          await logsStore.exportAllLogs(
            selectedTenantId.value,
            selectedEnv.value?.environment || 0,
            categoriesForDropDown.value
          )
        },
        badge: 'new'
      },
      {
        // Export all logs on current page
        label: `ページ内のログを出力 (${logs.value.length}件)`,
        icon: 'ph:file-csv-light',
        click: () => {
          if (selectedMode.value === 0) {
            logsStore.exportLogsToCSV(
              logs.value,
              `message_unit_logs`,
              categoriesForDropDown.value
            )
          } else {
            logsStore.exportLogsToCSV(
              logsFiltered.value,
              `session_unit_logs`,
              categoriesForDropDown.value
            )
          }
        }
      }
    ]
  ]

  if (currentSelectedLogs.length > 0) {
    const menuLabel
      = selectedMode.value === 0
        ? `選択中のログのみ (${currentSelectedLogs.length}件)`
        : `選択中のログのみ (${selectedSessionsCount.value}セッション)`

    menus.push([
      {
        label: menuLabel,
        icon: 'ph:file-csv-light',
        click: () => {
          if (selectedMode.value === 0) {
            logsStore.exportLogsToCSV(
              messageSelectedLogs.value,
              `message_unit_logs`,
              categoriesForDropDown.value
            )
          } else {
            // Get unique session IDs to avoid duplicates
            const uniqueSessionIds = new Set(
              sessionSelectedLogs.value.map(log => log.session_id)
            )
            const filteredLogs = logsFiltered.value.filter(log =>
              uniqueSessionIds.has(String(log.session_id))
            )
            logsStore.exportLogsToCSV(
              filteredLogs,
              `session_unit_logs`,
              categoriesForDropDown.value
            )
          }
        }
      },
      {
        label: '選択解除',
        icon: 'material-symbols:deselect',
        click: () => {
          if (selectedMode.value === 0) {
            messageSelectedLogs.value = []
          } else {
            sessionSelectedLogs.value = []
          }
        }
      }
    ])
  }
  return menus
}
// Advanced search functions
const onAdvancedSearch = (conditions: any) => {
  // Update logsFilter range
  logsFilter.value.range = conditions.range

  // Update logsSubFilter with search conditions
  logsSubFilter.value = {
    ...logsSubFilter.value,
    session_id: conditions.session_id || '',
    request_id: conditions.request_id || '',
    category_id: conditions.category_id || null,
    query: conditions.query || '',
    answer: conditions.answer || '',
    context_type: conditions.context_type,
    analyzed_action: conditions.analyzed_action,
    processed: conditions.processed
  }

  showAdvancedSearch.value = false
}

const onClearAdvancedSearch = () => {
  logsFilter.value = {
    range: { start: sub(new Date(), { days: 3 }), end: new Date() }
  }
  logsSubFilter.value = {}
}

// Check if any advanced search filter is applied
const hasAnyLogsFilter = computed(() => {
  return !!(
    logsSubFilter.value.session_id
    || logsSubFilter.value.request_id
    || logsSubFilter.value.category_id
    || logsSubFilter.value.query
    || logsSubFilter.value.answer
    || logsSubFilter.value.context_type
    || logsSubFilter.value.analyzed_action
    || logsSubFilter.value.processed
  )
})
</script>

<template>
  <UDashboardPage>
    <UDashboardPanel
      id="logs"
      :width="600"
      :resizable="{ min: 400, max: 600 }"
    >
      <UDashboardNavbar
        data-tour="logs-navbar"
        title="ログ情報"
        :badge="logPaginationTotal"
      >
        <template #right>
          <UTabs
            v-model="selectedMode"
            data-tour="logs-mode-tabs"
            :items="tabItems"
            :ui="{
              wrapper: '',
              list: {
                height: 'h-9',
                tab: { height: 'h-7', size: 'text-[13px]' }
              }
            }"
          />
        </template>
      </UDashboardNavbar>
      <UDashboardToolbar
        class="py-0 pl-2 dark:bg-gray-900/85 overflow-x-auto shadow-md"
      >
        <div class="flex items-center justify-between w-full gap-2">
          <div class="flex items-center gap-2">
            <UTooltip text="現在のページの全ての項目を選択/選択解除">
              <UButton
                data-tour="logs-select-all"
                :color="'white'"
                size="sm"
                :variant="'solid'"
              >
                <UCheckbox
                  :model-value="isAllCurrentPageSelected"
                  :indeterminate="
                    !isAllCurrentPageSelected && isSomeCurrentPageSelected
                  "
                  color="primary"
                  @update:model-value="handleSelectAll"
                />
              </UButton>
            </UTooltip>
            <BaseDateRangePicker
              v-model="logsFilter.range"
              data-tour="logs-date-range"
              size="sm"
              mini
            />
          </div>
          <UInput
            ref="keywordFilterRef"
            v-model="logsFilterKeyword"
            data-tour="logs-keyword-filter"
            icon="mingcute:search-3-line"
            autocomplete="off"
            placeholder="キーワードフィルタ..."
            size="sm"
            class="flex-1"
            @keydown.esc="$event.target.blur()"
          >
            <template #trailing>
              <UKbd value="/" />
            </template>
          </UInput>

          <div class="flex items-center gap-2 justify-between">
            <UTooltip
              v-if="hasAnyLogsFilter"
              text="高度な検索条件をクリア"
            >
              <UButton
                data-tour="logs-clear-search"
                icon="ant-design:clear-outlined"
                color="gray"
                size="sm"
                @click="onClearAdvancedSearch"
              />
            </UTooltip>
            <UChip
              size="lg"
              :show="hasAnyLogsFilter"
            >
              <UTooltip text="高度な検索">
                <UButton
                  data-tour="logs-advanced-search"
                  :icon="'fluent:slide-search-16-regular'"
                  :color="hasAnyLogsFilter ? 'primary' : 'gray'"
                  size="sm"
                  :variant="hasAnyLogsFilter ? 'outline' : 'solid'"
                  @click="showAdvancedSearch = !showAdvancedSearch"
                />
              </UTooltip>
            </UChip>
            <UDropdown
              data-tour="logs-export-dropdown"
              class="hidden group-hover:block z-50"
              :class="{
                block: true,
                hidden: false
              }"
              :items="rowMenus()"
              :popper="{ placement: 'bottom-start' }"
              :ui="{ width: 'w-64' }"
            >
              <UTooltip
                :text="
                  selectedLogsCount > 0
                    ? selectedMode === 0
                      ? `${selectedLogsCount}個のログが選択されています`
                      : `${selectedSessionsCount}セッション (${selectedLogsCount}ログ) が選択されています`
                    : 'ログをエクスポート'
                "
              >
                <UChip
                  size="lg"
                  :show="selectedLogsCount > 0"
                  :text="chipDisplayText"
                >
                  <UButton
                    class="row-menu"
                    :color="selectedLogsCount > 0 ? 'primary' : 'gray'"
                    :variant="selectedLogsCount > 0 ? 'outline' : 'solid'"
                    icon="tabler:file-download"
                    size="sm"
                    :loading="loadings.exportAllLogs"
                  />
                </UChip>
              </UTooltip>
            </UDropdown>
          </div>
        </div>
      </UDashboardToolbar>
      <LogsList
        v-model="selectedLog"
        v-model:selected-logs="currentSelectedLogs"
        data-tour="logs-list"
        :mode="selectedMode"
        :logs="logs"
        :logs-filter-keyword="logsFilterKeyword"
        :loading="loadings.searchLogs"
        :all-related-logs="allRelatedLogs"
        @export-selected-log="exportSelectedLogsToCSV"
      />

      <UDashboardToolbar>
        <template #left>
          <div class="flex items-center gap-1.5">
            <span class="text-sm leading-5">表示件数:</span>

            <USelect
              v-model="logPagination.pageCount"
              :options="[10, 20, 50, 100, 500, 1000]"
              class="w-20"
            />
          </div>
        </template>

        <template #right>
          <UPagination
            v-model="logPagination.page"
            :page-count="logPagination.pageCount"
            :total="logPaginationTotal"
            size="sm"
            :ui="{
              wrapper: 'flex items-center gap-1',
              rounded: '!rounded-full min-w-[32px] justify-center',
              default: {
                activeButton: {
                  variant: 'outline'
                }
              }
            }"
          />
        </template>
      </UDashboardToolbar>
    </UDashboardPanel>

    <UDashboardPanel
      v-model="isMailPanelOpen"
      collapsible
      grow
      side="right"
    >
      <template v-if="selectedLog">
        <UDashboardNavbar>
          <template #toggle>
            <UDashboardNavbarToggle icon="i-heroicons-x-mark" />

            <UDivider
              orientation="vertical"
              class="mx-1.5 lg:hidden"
            />
          </template>

          <template #left>
            <div class="text-sm font-semibold text-gray-900 dark:text-white">
              リクエストID: {{ (selectedLog as any)?.request_id }}
            </div>
          </template>

          <template #right />
        </UDashboardNavbar>

        <!-- ~/components/inbox/InboxMail.vue -->
        <LogDetail
          data-tour="logs-detail-panel"
          :logs="selectedMode === 0 ? [selectedLog] : allRelatedLogs"
        />
      </template>
      <div
        v-else
        class="flex-1 hidden lg:flex items-center justify-center"
      >
        <UIcon
          name="fluent:people-chat-24-filled"
          class="w-28 h-32 text-gray-400 dark:text-gray-500"
        />
      </div>
    </UDashboardPanel>

    <!-- Advanced Search Modal -->
    <LogsAdvancedSearch
      data-tour="logs-advanced-search-modal"
      :show="showAdvancedSearch"
      :conditions="{
        range: logsFilter.range,
        ...logsSubFilter
      }"
      @close="showAdvancedSearch = false"
      @search="onAdvancedSearch"
    />
  </UDashboardPage>
</template>
