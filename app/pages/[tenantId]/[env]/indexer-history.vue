<script setup lang="ts">
const searchIndexersStore = useSearchIndexersStore()
const { selectedTenantId, selectedEnvId } = useApp()
const route = useRoute()
const {
  loadings,
  indexerUpdateHistories,
  indexerUpdateHistoriesPagination,
  indexerUpdateHistoriesTotal
} = storeToRefs(searchIndexersStore)

onMounted(() => {
  if (!route.query.tour) {
    searchIndexersStore.searchIndexerHistories(
      selectedTenantId.value,
      selectedEnvId.value
    )
  }
})

const defaultColumns = [
  {
    key: 'id',
    label: 'ID',
    sortable: true
  },
  {
    key: 'indexer_name',
    label: 'インデクサー名',
    sortable: false
  },
  {
    key: 'action',
    label: 'アクション',
    sortable: false
  },
  {
    key: 'result',
    label: '結果',
    sortable: false
  },
  {
    key: 'created_at',
    label: '登録日時',
    sortable: false
  },
  {
    key: 'updated_at',
    label: '更新日時',
    sortable: false
  }
]
const sort = computed({
  get: () => ({
    column: 'id',
    direction: indexerUpdateHistoriesPagination.value.asc ? 'asc' : 'desc'
  }),
  set: (value) => {
    indexerUpdateHistoriesPagination.value.asc = value.direction === 'asc'
  }
})

// watch indexerUpdateHistoriesPagination.pageCount or indexerUpdateHistoriesPagination.page
// and fetch indexerUpdateHistories
watch(
  () => indexerUpdateHistoriesPagination.value,
  () => {
    searchIndexersStore.searchIndexerHistories(
      selectedTenantId.value,
      selectedEnvId.value
    )
  },
  { deep: true, immediate: true }
)

// watch indexerUpdateHistoriesPagination.value.pageCount, if it is changed, reset page to 1
watch(
  () => indexerUpdateHistoriesPagination.value.pageCount,
  () => {
    indexerUpdateHistoriesPagination.value.page = 1
  }
)

const actionLabels = {
  run: 'インデックス更新'
}
</script>

<template>
  <UDashboardPage data-tour="indexer-history">
    <UDashboardPanel grow>
      <UDashboardNavbar title="インデックス更新履歴">
        <template #right />
      </UDashboardNavbar>
      <UTable
        v-model:sort="sort"
        sort-mode="manual"
        :rows="indexerUpdateHistories"
        :columns="defaultColumns"
        :loading="loadings.searchIndexerHistories"
        class="w-full"
        :ui="{
          divide: 'divide-gray-200 dark:divide-gray-800',
          tr: { base: 'group' }
        }"
      >
        <template #result-data="{ row }">
          <div class="flex items-center gap-3 justify-between">
            <BaseResultBadge
              v-if="row.result !== null"
              v-model="row.result"
              class="capitalize flex-1 justify-center max-w-16"
            />
            <div
              v-else
            >
              <UBadge
                label="処理待ち"
                :color="'white'"
                :variant="'solid'"
                :trailing="false"
                size="sm"
                :ui="{
                  rounded: 'rounded-full'
                }"
              />
            </div>
          </div>
        </template>
        <template #action-data="{ row }">
          <div class="flex items-center gap-3 justify-between">
            <span>{{ actionLabels[row.action] }}</span>
          </div>
        </template>
        <template #created_at-data="{ row }">
          <div>
            <div>
              {{ formatDateTime(row.created_at) }}
            </div>
            <div class="text-gray-500 dark:text-gray-500 text-xs">
              作成者: {{ row.created_username }} ({{
                formatDistanceStrictDateTime(row.created_at)
              }})
            </div>
          </div>
        </template>

        <template #updated_at-data="{ row }">
          <div>
            <div>
              {{ formatDateTime(row.updated_at) }}
            </div>
            <div
              v-if="row.updated_at"
              class="text-gray-500 dark:text-gray-500 text-xs"
            >
              間隔: {{ formatDurationByDate(row.created_at, row.updated_at) }}
            </div>
          </div>
        </template>
      </UTable>
      <UDivider class="mt-0" />
      <UDashboardToolbar>
        <template #left>
          <div class="flex flex-wrap justify-between items-center">
            <div
              class="flex items-center gap-1.5"
              data-tour="indexer-history-pagecount"
            >
              <span class="text-sm leading-5">表示件数:</span>

              <USelect
                v-model="indexerUpdateHistoriesPagination.pageCount"
                :options="[3, 5, 10, 20, 30, 40]"
                class="w-20"
                size="xs"
              />
            </div>
            <UDivider
              class="mx-3 h-full py-1"
              orientation="vertical"
            />
          </div>
        </template>

        <template #right>
          <UPagination
            v-if="
              indexerUpdateHistoriesPagination.pageCount
                < indexerUpdateHistoriesTotal
            "
            v-model="indexerUpdateHistoriesPagination.page"
            :page-count="indexerUpdateHistoriesPagination.pageCount"
            :total="indexerUpdateHistoriesTotal"
            size="sm"
            data-tour="indexer-history-pagination"
          />
        </template>
      </UDashboardToolbar>
    </UDashboardPanel>
  </UDashboardPage>
</template>
