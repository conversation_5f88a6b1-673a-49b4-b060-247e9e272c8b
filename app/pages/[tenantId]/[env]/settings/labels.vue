<script setup lang="ts">
// Define required permissions for this page
definePageMeta({
  middleware: ['authentication', 'role-guard'],
  requiredPermissions: ['view_labels']
})

const { selectedTenantId, selectedEnvId, isSelectedEnvIsProd } = useApp()
const route = useRoute()
const labelsStore = useLabelsStore()
const { loadings, labels, errors, isFormOpen } = storeToRefs(labelsStore)
const confirm = useConfirm()
onMounted(() => {
  if (!route.query.tour) {
    labelsStore.fetchLabels(selectedTenantId.value, selectedEnvId.value)
  }
})

const newLabel = ref(null)
const onSubmit = async (data: any) => {
  if (selectedLabel.value) {
    const result = await labelsStore.updateLabel(
      selectedTenantId.value,
      selectedEnvId.value,
      {
        key: selectedLabel.value.key,
        label: data?.label
      }
    )
    if (result) {
      isFormOpen.value = false
    }
  } else {
    const result = await labelsStore.createLabel(
      selectedTenantId.value,
      selectedEnvId.value,
      {
        label: data?.label
      }
    )
    if (result) {
      newLabel.value = result
      isFormOpen.value = false
    }
  }
}

const selectedLabel = ref(null)

const onCreate = () => {
  selectedLabel.value = null
  isFormOpen.value = true
}

const onUpdate = (label: any) => {
  selectedLabel.value = label
  isFormOpen.value = true
}

const onDelete = (label: any) => {
  confirm.show({
    title: `ラベル削除の確認`,
    description: `ラベル「${label.label || label.key}」を削除しますか？`,
    confirmText: '削除',
    onConfirm: async () => {
      await labelsStore.deleteLabel(
        selectedTenantId.value,
        selectedEnvId.value,
        label.key
      )
    }
  })
}
</script>

<template>
  <UDashboardPanelContent class="p-0">
    <BaseEmptyList
      v-if="!labels.length"
      data-tour="no_labels"
      text="ラベルがありません"
      init-button
      :disabled="isSelectedEnvIsProd"
      @init="onCreate"
    />
    <LabelsTable
      v-else
      data-tour="label-table"
      :labels="labels"
      :loading="loadings.fetchLabels"
      @edit="onUpdate"
      @delete="onDelete"
      @create="onCreate"
      @refresh="labelsStore.fetchLabels(selectedTenantId, selectedEnvId, true)"
    />
    <UDashboardModal
      v-model="isFormOpen"
      data-tour="label-modify-dialog"
      :title="selectedLabel ? 'ラベルを編集' : '新規ラベル作成'"
      :description="
        selectedLabel
          ? 'ラベルの情報を編集します。'
          : '新しいラベルを作成します。'
      "
      :ui="{ width: 'sm:max-w-md' }"
    >
      <!-- ~/components/settings/MembersForm.vue -->
      <LabelsForm
        :loading="loadings.createLabel || loadings.updateLabel"
        v-bind="selectedLabel"
        :error="errors.createLabel || errors.updateLabel"
        @close="isFormOpen = false"
        @submit="onSubmit"
      />
    </UDashboardModal>
  </UDashboardPanelContent>
</template>
