<script setup lang="ts">
// Define required permissions for this page
import { debounce } from 'lodash'
import type {
  TenantWithEnvironments,
  CreateTenantPayload,
  UpdateTenantPayload
} from '~/types/tenant'

definePageMeta({
  middleware: ['authentication', 'role-guard'],
  requiredPermissions: ['view_tenants']
})

const isFormOpen = ref(false)
const isCreationProcessOpen = ref(false)

const toast = useToast()
const tenantsStore = useTenantsStore()
const environmentsStore = useEnvironmentsStore()
const settingsStore = useSettingsStore()
const {
  loadings,
  tenantsWithEnvironments,
  tenantsPagination,
  tenantsTotal,
  tenantsFilter,
  selectedTenants
} = storeToRefs(tenantsStore)
const { environments } = storeToRefs(environmentsStore)
const confirm = useConfirm()
const { PERMISSIONS } = useAppPermissions()

const newTenant = ref<CreateTenantPayload | null>(null)
const onSubmit = (data: CreateTenantPayload | UpdateTenantPayload) => {
  if (selectedTenant.value) {
    tenantsStore.updateTenant(
      selectedTenant.value.id,
      data as UpdateTenantPayload
    )
  } else {
    // tenantsStore.createTenant(data)
    newTenant.value = data as CreateTenantPayload
    isCreationProcessOpen.value = true
  }
  isFormOpen.value = false
}

const selectedTenant = ref<TenantWithEnvironments | null>(null)

const onCreate = () => {
  selectedTenant.value = null
  isFormOpen.value = true
}

const onUpdate = (tenant: TenantWithEnvironments) => {
  selectedTenant.value = tenant
  isFormOpen.value = true
}

const onDelete = async (tenant: TenantWithEnvironments) => {
  // First fetch all tenants
  await environmentsStore.fetchAllEnvs(tenant.id)
  confirm.show({
    title: `テナント削除の確認`,
    description: `テナント「${
      tenant.description || tenant.id
    }」を削除しますか？`,
    confirmText: '削除',
    onConfirm: async () => {
      let isDeleteSuccessfully = true
      if (environments.value[tenant.id]?.length > 0) {
        // Delete all existing environments
        for (const env of environments.value[tenant.id]) {
          isDeleteSuccessfully = await environmentsStore.deleteEnvironment(
            tenant.id,
            env.id
          )
          if (!isDeleteSuccessfully) break
        }
      }
      // All environments are deleted successfully
      if (isDeleteSuccessfully) {
        isDeleteSuccessfully = await tenantsStore.deleteTenant(tenant.id)
      }
      if (isDeleteSuccessfully) {
        toast.add({
          title: '削除しました',
          description: 'テナントを削除しました',
          color: 'green'
        })
      }
    }
  })
}

const onUpdateMaintenance = (data: {
  tenantId: string
  envId: string
  key: string
  value: boolean
}) => {
  // Refresh the settings to ensure UI is in sync
}

const onToggleTenantStatus = async (tenant: TenantWithEnvironments) => {
  confirm.show({
    title: 'ステータス変更',
    description: `このテナントのステータスを${
      tenant.enabled ? '無効' : '有効'
    }に変更しますか？`,
    confirmText: '変更',
    onConfirm: async () => {
      const result = await tenantsStore.updateTenant(tenant.id, {
        ...tenant,
        enabled: !tenant.enabled
      })
      if (result) {
        toast.add({
          title: '成功',
          description: 'テナントのステータスを変更しました',
          color: 'green'
        })
      }
    }
  })
}

const isFilterEmpty = computed(() => {
  return Object.values(tenantsFilter.value).every(value => !value)
})

const refresh = () => {
  tenantsStore.fetchTenants()
}

const debounceRefresh = debounce(refresh, 1000)

watch(
  () => tenantsPagination.value.pageCount,
  () => {
    tenantsPagination.value.page = 1
  }
)

watch(
  () => tenantsPagination.value,
  () => {
    refresh()
  },
  { deep: true, immediate: true }
)

watch(
  [() => tenantsFilter.value.id, () => tenantsFilter.value.description],
  () => {
    debounceRefresh()
  }
)

const onDeleteMany = () => {
  const tenantIds = selectedTenants.value.map(tenant => tenant.id)
  confirm.show({
    title: `テナント削除の確認`,
    description: `テナント「${tenantIds.join(', ')}」を削除しますか？`,
    confirmText: '削除',
    onConfirm: async () => {
      let isDeleteSuccessfully = true
      for (const tenantId of tenantIds) {
        // First fetch all environments for this tenant
        await environmentsStore.fetchAllEnvs(tenantId)
        if (environments.value[tenantId]?.length > 0) {
          // Delete all existing environments
          for (const env of environments.value[tenantId]) {
            isDeleteSuccessfully = await environmentsStore.deleteEnvironment(
              tenantId,
              env.id
            )
            if (!isDeleteSuccessfully) break
          }
        }
        // All environments are deleted successfully
        if (isDeleteSuccessfully) {
          isDeleteSuccessfully = await tenantsStore.deleteTenant(tenantId)
        }
        if (!isDeleteSuccessfully) break
      }
      if (isDeleteSuccessfully) {
        toast.add({
          title: '削除しました',
          description: 'テナントを削除しました',
          color: 'green'
        })
        selectedTenants.value = []
        refresh()
      }
    }
  })
}

onUnmounted(() => {
  tenantsStore.resetFilters()
})

// Load custom settings for all tenants and environments
onMounted(async () => {
  await tenantsStore.fetchTenants()
})
</script>

<template>
  <UDashboardPanelContent class="p-0">
    <TenantsTable
      v-model:selected-tenants="selectedTenants"
      :tenants="tenantsWithEnvironments"
      :loading="loadings.fetchTenants"
      :tenants-pagination="tenantsPagination"
      :tenants-total="tenantsTotal"
      :tenants-filter="tenantsFilter"
      @edit="onUpdate"
      @delete="onDelete"
      @delete-many="onDeleteMany"
      @create="onCreate"
      @update-maintenance="onUpdateMaintenance"
      @toggle-tenant-status="onToggleTenantStatus"
      @refresh="refresh"
    >
      <template #actions>
        <PermissionGuard :permission="PERMISSIONS.CREATE_TENANT">
          <UButton
            label="新規作成"
            icon="i-heroicons-plus"
            color="gray"
            size="sm"
            @click="onCreate"
          />
        </PermissionGuard>
      </template>
    </TenantsTable>

    <UDashboardModal
      v-model="isFormOpen"
      :title="selectedTenant ? 'テナントを編集' : '新規テナント作成'"
      :description="
        selectedTenant
          ? 'テナントの情報を編集します。'
          : '新しいテナントを作成します。'
      "
      :ui="{ width: 'sm:max-w-md' }"
    >
      <!-- ~/components/settings/MembersForm.vue -->
      <TenantsForm
        :loading="loadings.createTenant || loadings.updateTenant"
        v-bind="selectedTenant"
        @close="isFormOpen = false"
        @submit="onSubmit"
      />
    </UDashboardModal>

    <UDashboardModal
      v-model="isCreationProcessOpen"
      title="新規テナント作成中"
      description="
        テナントを作成しています。しばらくお待ちください。
      "
      :ui="{ width: 'sm:max-w-md' }"
    >
      <!-- ~/components/settings/MembersForm.vue -->
      <TenantsCreationProcess
        v-bind="newTenant"
        @close="isCreationProcessOpen = false"
      />
    </UDashboardModal>
  </UDashboardPanelContent>
</template>
