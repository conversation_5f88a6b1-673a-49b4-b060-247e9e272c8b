<template>
  <UDashboardPanelContent class="p-4">
    <div class="space-y-6 w-full">
      <!-- Header -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
            使用量比較
          </h1>
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
            複数テナントの使用量統計を比較できます
          </p>
        </div>
      </div>
      <div class="space-y-6">
        <!-- Search Form -->
        <UCard>
          <template #header>
            <div class="flex items-center justify-between">
              <h2 class="text-lg font-semibold">
                テナント追加
              </h2>
              <UButton
                variant="ghost"
                size="sm"
                @click="resetFilters"
              >
                リセット
              </UButton>
            </div>
          </template>

          <div class="space-y-6">
            <!-- Tenant Selection -->
            <div class="space-y-2">
              <label
                class="text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                テナント <span class="text-red-500">*</span>
              </label>
              <USelectMenu
                v-model="selectedTenant"
                :options="tenantOptions"
                placeholder="テナントを選択してください"
                value-attribute="id"
                option-attribute="label"
                class="w-64"
              />
            </div>

            <!-- Date Range -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="space-y-2">
                <label
                  class="text-sm font-medium text-gray-700 dark:text-gray-300"
                >
                  開始日
                </label>
                <UInput
                  v-model="searchFilters.from_date"
                  type="date"
                  placeholder="YYYY-MM-DD"
                  icon="i-heroicons-calendar-20-solid"
                />
              </div>
              <div class="space-y-2">
                <label
                  class="text-sm font-medium text-gray-700 dark:text-gray-300"
                >
                  終了日
                </label>
                <UInput
                  v-model="searchFilters.to_date"
                  type="date"
                  placeholder="YYYY-MM-DD"
                  icon="i-heroicons-calendar-20-solid"
                />
              </div>
            </div>

            <!-- Environment Filters -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="space-y-2">
                <label
                  class="text-sm font-medium text-gray-700 dark:text-gray-300"
                >
                  環境ID
                </label>
                <UInput
                  v-model="searchFilters.env_id"
                  placeholder="環境IDを入力"
                  icon="i-heroicons-server-20-solid"
                />
              </div>
              <div class="space-y-2">
                <label
                  class="text-sm font-medium text-gray-700 dark:text-gray-300"
                >
                  環境タイプ
                </label>
                <USelectMenu
                  v-model="searchFilters.environment"
                  :options="environmentTypeOptions"
                  placeholder="環境タイプを選択"
                  value-attribute="value"
                  option-attribute="label"
                  :nullable="true"
                />
              </div>
            </div>

            <!-- Session and Request IDs -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="space-y-2">
                <label
                  class="text-sm font-medium text-gray-700 dark:text-gray-300"
                >
                  リクエストID
                </label>
                <UInput
                  v-model="searchFilters.request_id"
                  placeholder="リクエストIDを入力"
                  icon="i-heroicons-identification-20-solid"
                />
              </div>
              <div class="space-y-2">
                <label
                  class="text-sm font-medium text-gray-700 dark:text-gray-300"
                >
                  セッションID
                </label>
                <UInput
                  v-model="searchFilters.session_id"
                  placeholder="セッションIDを入力"
                  icon="i-heroicons-chat-bubble-left-right-20-solid"
                />
              </div>
            </div>

            <!-- Add Tenant Button -->
            <div class="flex items-center justify-between">
              <div class="text-sm text-gray-500">
                * 必須項目
              </div>
              <UButton
                :loading="isAddingTenant"
                :disabled="!selectedTenant"
                size="lg"
                @click="addTenantToComparison"
              >
                テナントを追加
              </UButton>
            </div>

            <div
              v-if="addTenantError"
              class="text-red-500 text-sm"
            >
              エラー: {{ addTenantError }}
            </div>
          </div>
        </UCard>
      </div>
      <div class="space-y-6">
        <UsageComparisonSummary
          :tenants="usageStore.tenantUsageList"
          :summary="usageStore.comparisonSummary"
          @export-csv="exportComparisonCSV"
          @clear-all="clearAllComparison"
          @remove-tenant="removeTenantFromComparison"
        />

        <UsageComparisonChart
          v-if="usageStore.comparisonData.length > 0"
          :tenants="usageStore.tenantUsageList"
        />
      </div>
      <!-- Tabs -->
      <template
        v-for="tenant in usageStore.tenantUsageList.filter(
          (t) => t.data !== null
        )"
        :key="tenant.tenantId"
      >
        <div class="space-y-6">
          <!-- Tenant Info -->
          <UCard>
            <template #header>
              <div class="flex items-center justify-between">
                <div>
                  <h2 class="text-lg font-semibold">
                    {{ tenant.tenantName }}
                  </h2>
                  <p class="text-sm text-gray-500">
                    {{ tenant.tenantId }}
                  </p>
                </div>
                <UButton
                  variant="ghost"
                  size="sm"
                  icon="i-heroicons-x-mark"
                  color="red"
                  @click="removeTenantFromComparison(tenant.tenantId)"
                >
                  削除
                </UButton>
              </div>
            </template>

            <div class="flex items-center space-x-4 text-sm">
              <span class="font-medium">期間:</span>
              <span>{{ tenant.data?.from_date }}</span>
              <span>→</span>
              <span>{{ tenant.data?.to_date }}</span>
            </div>
          </UCard>

          <!-- Individual Tenant Usage Stats -->
          <UCard>
            <template #header>
              <h3 class="text-lg font-semibold">
                使用量概要
              </h3>
            </template>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <div
                  class="text-sm text-blue-600 dark:text-blue-400 font-medium"
                >
                  Azure Search
                </div>
                <div
                  class="text-2xl font-bold text-blue-700 dark:text-blue-300"
                >
                  {{ tenant.data?.usage.azure_search.total || 0 }}
                </div>
              </div>

              <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                <div
                  class="text-sm text-green-600 dark:text-green-400 font-medium"
                >
                  Batch
                </div>
                <div
                  class="text-2xl font-bold text-green-700 dark:text-green-300"
                >
                  {{ tenant.data?.usage.batch.total || 0 }}
                </div>
              </div>

              <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                <div
                  class="text-sm text-purple-600 dark:text-purple-400 font-medium"
                >
                  Chat
                </div>
                <div
                  class="text-2xl font-bold text-purple-700 dark:text-purple-300"
                >
                  {{ tenant.data?.usage.chat.total || 0 }}
                </div>
              </div>

              <div class="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
                <div
                  class="text-sm text-orange-600 dark:text-orange-400 font-medium"
                >
                  Google Search
                </div>
                <div
                  class="text-2xl font-bold text-orange-700 dark:text-orange-300"
                >
                  {{ tenant.data?.usage.google_search.total || 0 }}
                </div>
              </div>

              <div class="bg-indigo-50 dark:bg-indigo-900/20 p-4 rounded-lg">
                <div
                  class="text-sm text-indigo-600 dark:text-indigo-400 font-medium"
                >
                  Knowledge
                </div>
                <div
                  class="text-2xl font-bold text-indigo-700 dark:text-indigo-300"
                >
                  {{ tenant.data?.usage.knowledge.total || 0 }}
                </div>
              </div>
            </div>
          </UCard>
        </div>
      </template>
    </div>
  </UDashboardPanelContent>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { EnvironmentType, type UsageSearchFilters } from '~/stores/usage'

// Page meta
definePageMeta({
  layout: 'default'
})

// Stores
const usageStore = useUsageStore()
const tenantsStore = useTenantsStore()
const authStore = useAuthStore()

// Reactive data
const selectedTenant = ref('')
const searchFilters = ref<UsageSearchFilters>({
  from_date: '',
  to_date: '',
  env_id: '',
  environment: null,
  request_id: '',
  session_id: ''
})
const isAddingTenant = ref(false)
const addTenantError = ref('')

// Computed
const tenantOptions = computed(() => {
  return tenantsStore.allTenants.map(tenant => ({
    id: tenant.id,
    label: tenant.description || tenant.id
  }))
})

const environmentTypeOptions = computed(() => [
  { label: 'Production', value: EnvironmentType.PRODUCTION },
  { label: 'Staging', value: EnvironmentType.STAGING }
])

const activeTab = computed({
  get: () => {
    return tabItems.value[0]
  },
  set: value => usageStore.setActiveTab(value)
})

const tabItems = computed(() => {
  const items = [
    {
      key: 'search',
      label: 'テナント追加',
      icon: 'i-heroicons-plus'
    },
    {
      key: 'comparison',
      label: `比較 (${usageStore.comparisonData.length})`,
      icon: 'i-heroicons-chart-bar'
    }
  ]

  // Add tabs for each tenant with data
  usageStore.tenantUsageList
    .filter(tenant => tenant.data !== null)
    .forEach((tenant) => {
      items.push({
        key: `tenant-${tenant.tenantId}`,
        label: tenant.tenantName,
        icon: 'i-heroicons-building-office'
      })
    })

  return items
})

// Methods
const addTenantToComparison = async () => {
  if (!selectedTenant.value) return

  try {
    isAddingTenant.value = true
    addTenantError.value = ''

    // Build filters object, only including non-empty values
    const filters: UsageSearchFilters = {}

    if (searchFilters.value.from_date) {
      filters.from_date = searchFilters.value.from_date
    }
    if (searchFilters.value.to_date) {
      filters.to_date = searchFilters.value.to_date
    }
    if (searchFilters.value.env_id) {
      filters.env_id = searchFilters.value.env_id
    }
    if (
      searchFilters.value.environment !== null
      && searchFilters.value.environment !== undefined
    ) {
      filters.environment = searchFilters.value.environment
    }
    if (searchFilters.value.request_id) {
      filters.request_id = searchFilters.value.request_id
    }
    if (searchFilters.value.session_id) {
      filters.session_id = searchFilters.value.session_id
    }

    // Get tenant name
    const tenantName
      = tenantsStore.allTenants.find(t => t.id === selectedTenant.value)
        ?.description || selectedTenant.value

    await usageStore.addTenantToComparison(
      selectedTenant.value,
      tenantName,
      filters
    )

    // Switch to comparison tab after adding
    usageStore.setActiveTab('comparison')

    // Reset form
    selectedTenant.value = ''
  } catch (error: any) {
    addTenantError.value = error?.message || 'テナントの追加に失敗しました'
  } finally {
    isAddingTenant.value = false
  }
}

const removeTenantFromComparison = (tenantId: string) => {
  usageStore.removeTenantFromComparison(tenantId)

  // If we're on the removed tenant's tab, switch to comparison tab
  if (usageStore.activeTab === `tenant-${tenantId}`) {
    usageStore.setActiveTab('comparison')
  }
}

const clearAllComparison = () => {
  usageStore.clearComparisonData()
  usageStore.setActiveTab('search')
}

const exportComparisonCSV = () => {
  try {
    usageStore.exportComparisonToCSV()
  } catch (error: any) {
    // Handle error (could show toast notification)
    console.error('CSV export failed:', error)
  }
}

const resetFilters = () => {
  searchFilters.value = {
    from_date: '',
    to_date: '',
    env_id: '',
    environment: null,
    request_id: '',
    session_id: ''
  }
  addTenantError.value = ''
}

// Set default date range (last 30 days)
const setDefaultDateRange = () => {
  const today = new Date()
  const thirtyDaysAgo = new Date(today)
  thirtyDaysAgo.setDate(today.getDate() - 30)

  searchFilters.value.to_date = today.toISOString().split('T')[0]
  searchFilters.value.from_date = thirtyDaysAgo.toISOString().split('T')[0]
}

// Lifecycle
onMounted(async () => {
  // Fetch all tenants for selection
  if (authStore.isOperator) {
    await tenantsStore.fetchAllTenants()
  }

  // Set default date range
  setDefaultDateRange()

  // Set initial active tab
  if (usageStore.tenantUsageList.length > 0) {
    usageStore.setActiveTab('comparison')
  } else {
    usageStore.setActiveTab('search')
  }
})

// Clear data when leaving the page
onUnmounted(() => {
  // Don't clear comparison data on unmount to preserve state
  // usageStore.clearComparisonData()
})
</script>
