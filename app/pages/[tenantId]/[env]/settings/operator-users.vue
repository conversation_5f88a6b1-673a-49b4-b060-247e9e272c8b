<script setup lang="ts">
// Define required permissions for this page
import { debounce } from 'lodash'

const authStore = useAuthStore()
const { lockedUsers } = storeToRefs(authStore)

definePageMeta({
  middleware: ['authentication', 'role-guard'],
  requiredPermissions: ['view_tenants']
})
const { selectedTenantId } = useApp()
const isFormOpen = ref(false)
const usersOperatorStore = useUsersOperatorStore()
const usersStore = useUsersStore()
const {
  loadings,
  operatorUsers,
  errors,
  operatorUsersPagination,
  operatorUsersTotal,
  operatorUsersFilter,
  selectedOperatorUsers
} = storeToRefs(usersOperatorStore)
const {
  loadings: usersStoreLoadings,
  errors: usersStoreErrors
} = storeToRefs(usersStore)
const confirm = useConfirm()
const toast = useToast()
const { PERMISSIONS } = useAppPermissions()
const newLabel = ref(null)
const onSubmit = async (data: any) => {
  if (selectedUser.value) {
    const result = await usersStore.updateUser(
      selectedUser.value.username,
      selectedTenantId.value,
      { ...data, enabled: true }
    )
    if (result) {
      isFormOpen.value = false
      usersOperatorStore.fetchAllOperatorUsers()
    }
  } else {
    const result = await usersOperatorStore.createOperatorUser(data)
    if (result) {
      newLabel.value = result
      isFormOpen.value = false
      toast.add({
        title: '成功',
        description: 'PNL管理者ユーザを追加しました。',
        color: 'green'
      })
    }
  }
}

onUnmounted(() => {
  usersOperatorStore.resetFilters()
})

const isFilterEmpty = computed(() => {
  return Object.values(operatorUsersFilter.value).every(value => !value)
})

const refresh = () => {
  usersOperatorStore.fetchAllOperatorUsers()
  authStore.getLockedUsers()
}
const debounceRefresh = debounce(refresh, 1000)

watch(
  () => operatorUsersPagination.value.pageCount,
  () => {
    operatorUsersPagination.value.page = 1
  }
)

watch(
  () => operatorUsersPagination.value,
  () => {
    refresh()
  },
  { deep: true, immediate: true }
)

watch(
  [() => operatorUsersFilter.value.username, () => operatorUsersFilter.value.status],
  () => {
    debounceRefresh()
  }
)

const selectedUser = ref<any>(null)
const isUpdateMode = computed(() => Boolean(selectedUser.value))
const onCreate = () => {
  selectedUser.value = null
  isFormOpen.value = true
}

const onUpdate = (user: any) => {
  selectedUser.value = user
  isFormOpen.value = true
}

const onDelete = (user: any) => {
  confirm.show({
    title: `PNL管理者ユーザ削除の確認`,
    description: `ユーザ「${user.username || user.key}」を削除しますか？`,
    confirmText: '削除',
    onConfirm: async () => {
      await usersOperatorStore.deleteOperatorUser(user.username)
    }
  })
}

const onDeleteMany = () => {
  const usernames = selectedOperatorUsers.value.map(user => user.username)
  confirm.show({
    title: `PNL管理者ユーザ削除の確認`,
    description: `ユーザ「${usernames.join(', ')}」を削除しますか？`,
    confirmText: '削除',
    onConfirm: async () => {
      const response = await usersOperatorStore.deleteManyOperatorUsers(usernames)
      if (!response) {
        toast.add({
          id: 'error',
          title: 'エラー',
          description: 'PNL管理者ユーザ削除に失敗しました。',
          color: 'red'
        })
      }
    }
  })
}

const onReleaseUserLock = (username: string) => {
  confirm.show({
    title: `PNL管理者ユーザーロック解除の確認`,
    description: `PNL管理者ユーザ「${username}」のロックを解除しますか？`,
    confirmText: '解除',
    onConfirm: async () => {
      const response = await authStore.releaseUserLock(username)
      if (!response) {
        toast.add({
          id: 'error',
          title: 'エラー',
          description: 'PNL管理者ユーザ解除に失敗しました。',
          color: 'red'
        })
      } else {
        toast.add({
          title: '解除完了',
          description: 'PNL管理者ユーザを解除しました。',
          color: 'green'
        })
        authStore.getLockedUsers()
      }
    }
  })
}

authStore.getLockedUsers()
</script>

<template>
  <UDashboardPanelContent class="p-0">
    <BaseEmptyList
      v-if="!operatorUsers.length && !loadings.fetchAllOperatorUsers && isFilterEmpty"
      icon="dashicons:superhero-alt"
      text="まだPNL管理者ユーザがいません"
      init-button-label="PNL管理者ユーザを追加"
      init-button
      @init="onCreate"
    />
    <OperatorUsersTable
      v-else
      v-model:selected-users="selectedOperatorUsers"
      :users="operatorUsers"
      :locked-users="lockedUsers"
      :loading="loadings.fetchAllOperatorUsers"
      :users-pagination="operatorUsersPagination"
      :users-total="operatorUsersTotal"
      :users-filter="operatorUsersFilter"
      @edit="onUpdate"
      @delete="onDelete"
      @delete-many="onDeleteMany"
      @create="onCreate"
      @refresh="refresh"
      @release-user-lock="onReleaseUserLock"
    >
      <template #actions>
        <PermissionGuard :permission="PERMISSIONS.CREATE_USER">
          <RoleBasedButton
            label="PNL管理者ユーザ追加"
            icon="icomoon-free:user-plus"
            color="gray"
            size="sm"
            @click="onCreate"
          />
        </PermissionGuard>
      </template>
    </OperatorUsersTable>

    <UDashboardModal
      v-model="isFormOpen"
      :title="selectedUser ? 'PNL管理者ユーザを編集' : '新規PNL管理者ユーザ登録'"
      :description="
        selectedUser
          ? 'PNL管理者ユーザの情報を編集します。'
          : '新しいPNL管理者ユーザを作成します。'
      "
      :ui="{ width: 'sm:max-w-md' }"
    >
      <OperatorUserForm
        :users-pagination="operatorUsersPagination"
        :loading="loadings.createOperatorUser || usersStoreLoadings.updateUser"
        v-bind="selectedUser || {}"
        :is-update-mode="isUpdateMode"
        :error="errors.createOperatorUser || usersStoreErrors.updateUser"
        @close="isFormOpen = false"
        @submit="onSubmit"
      />
    </UDashboardModal>
  </UDashboardPanelContent>
</template>
