<script setup lang="ts">
// Define required permissions for this page
definePageMeta({
  middleware: ['authentication', 'role-guard'],
  requiredPermissions: ['view_api_action_logs']
})

const { selectedTenantId, selectedEnv } = useApp()
const managementStore = useManagementStore()
const {
  selectedApiActionLog,
  apiActionLogsFilterKeyword,
  apiActionLogsFiltered,
  apiActionLogsPagination,
  apiActionLogsPageTotal,
  apiActionLogsTotalCount,
  loadings
} = storeToRefs(managementStore)

const selectedLogs = ref([])
const isModalOpen = ref(false)
const selectedLogDetail = ref<any>(null)
const showDiffView = ref(true)

// Show log details in modal
const showLogDetails = (log: any) => {
  selectedLogDetail.value = log
  isModalOpen.value = true
}

// Parse JSON safely
const safeParseJSON = (jsonString: any) => {
  if (!jsonString) return null
  try {
    return JSON.parse(jsonString)
  } catch (error) {
    console.error('Error parsing JSON:', error)
    return jsonString // Return the original string if parsing fails
  }
}

// Compare original and new data to create a diff view
const getDiffData = (): Array<{
  key: string
  originalValue: any
  newValue: any
  status: string
}> => {
  if (
    !selectedLogDetail.value
    || !selectedLogDetail.value.original_data
    || !selectedLogDetail.value.new_data
  ) {
    return []
  }

  const originalData = safeParseJSON(selectedLogDetail.value.original_data)
  const newData = safeParseJSON(selectedLogDetail.value.new_data)

  if (!originalData || !newData) {
    return []
  }

  // Create a combined object with all keys from both objects
  const allKeys = new Set([
    ...Object.keys(originalData),
    ...Object.keys(newData)
  ])

  const diffData: Array<{
    key: string
    originalValue: any
    newValue: any
    status: string
  }> = []

  // Compare each key and create a diff entry
  allKeys.forEach((key) => {
    const originalValue = originalData[key]
    const newValue = newData[key]
    const hasOriginal = key in originalData
    const hasNew = key in newData

    let status = 'unchanged'
    if (!hasOriginal) {
      status = 'added'
    } else if (!hasNew) {
      status = 'removed'
    } else if (JSON.stringify(originalValue) !== JSON.stringify(newValue)) {
      status = 'modified'
    }

    if (status !== 'unchanged') {
      diffData.push({
        key,
        originalValue: hasOriginal ? originalValue : undefined,
        newValue: hasNew ? newValue : undefined,
        status
      })
    }
  })

  return diffData
}

// Watch for changes in pagination
watch(
  () => apiActionLogsPagination.value,
  () => {
    managementStore.fetchApiActionLogs()
  },
  { deep: true, immediate: true }
)

// Watch for pageCount changes and reset page to 1
watch(
  () => apiActionLogsPagination.value.pageCount,
  () => {
    apiActionLogsPagination.value.page = 1
  }
)
// Export All logs to CSV
const exportAllLogs = () => {
  managementStore.exportAllLogs()
}

// Export logs to CSV
const exportLogsToCSV = () => {
  managementStore.exportApiActionLogsToCSV(
    apiActionLogsFiltered.value,
    'api_action_logs'
  )
}

// Export selected logs to CSV
const exportSelectedLogsToCSV = () => {
  if (selectedLogs.value.length > 0) {
    managementStore.exportApiActionLogsToCSV(
      selectedLogs.value,
      'selected_api_action_logs'
    )
  }
}

// Refresh logs
const refreshLogs = () => {
  managementStore.fetchApiActionLogs()
}
</script>

<template>
  <UDashboardPage>
    <UDashboardPanel grow>
      <UDashboardNavbar>
        <template #left>
          <h1 class="text-xl font-semibold">
            API操作ログ
          </h1>
        </template>
      </UDashboardNavbar>

      <UDashboardToolbar>
        <template #left>
          <!-- <div class="flex items-center gap-3">
            <UInput
              v-model="apiActionLogsFilterKeyword"
              icon="i-heroicons-magnifying-glass-20-solid"
              placeholder="検索..."
              size="sm"
              class="w-64"
            />
          </div> -->
        </template>
        <template #right>
          <div class="flex items-center gap-3">
            <UButton
              color="white"
              icon="charm:download"
              size="xs"
              :loading="loadings.exportAllLogs"
              @click="exportAllLogs"
            >
              全てのログを出力
            </UButton>
            <UButton
              color="white"
              icon="charm:download"
              size="xs"
              @click="exportLogsToCSV"
            >
              ページ内のログを出力
            </UButton>
            <UButton
              v-if="selectedLogs.length > 0"
              color="white"
              icon="charm:download"
              size="xs"
              @click="exportSelectedLogsToCSV"
            >
              選択したログをCSV出力
            </UButton>
            <UButton
              color="white"
              icon="mdi:refresh"
              size="xs"
              :loading="loadings.fetchApiActionLogs"
              @click="refreshLogs"
            >
              更新
            </UButton>
          </div>
        </template>
      </UDashboardToolbar>

      <UDashboardPanelContent class="p-0">
        <BaseLoading v-if="loadings.fetchApiActionLogs" />
        <div
          v-else-if="!apiActionLogsFiltered.length"
          class="flex flex-col gap-3 items-center justify-center h-full py-10"
        >
          <UIcon
            name="ix:box-open"
            class="text-7xl text-gray-500 dark:text-gray-600"
          />
          <p class="text-gray-500 dark:text-gray-600">
            ログが見つかりませんでした。
          </p>
        </div>
        <div v-else>
          <UTable
            v-model="selectedLogs"
            :rows="apiActionLogsFiltered"
            :columns="[
              {
                key: 'created_at',
                label: '日時',
                sortable: true
              },
              {
                key: 'created_username',
                label: 'ユーザ名',
                sortable: true
              },
              {
                key: 'user_id',
                label: 'ユーザID',
                sortable: true
              },
              {
                key: 'action',
                label: 'アクション',
                sortable: true
              },
              {
                key: 'table_name',
                label: 'テーブル名',
                sortable: true
              },
              {
                key: 'env_id',
                label: '環境ID',
                sortable: true
              },
              {
                key: 'actions',
                label: '詳細',
                sortable: false
              }
            ]"
            :ui="{
              td: {
                base: 'whitespace-normal'
              },
              tr: {
                base: 'hover:bg-gray-100 dark:hover:bg-gray-800'
              }
            }"
          >
            <template #created_at-data="{ row }">
              {{ new Date(row.created_at).toLocaleString() }}
            </template>

            <template #actions-data="{ row }">
              <UButton
                color="gray"
                variant="ghost"
                icon="i-heroicons-eye"
                size="xs"
                @click="showLogDetails(row)"
              >
                詳細
              </UButton>
            </template>
          </UTable>

          <div class="flex justify-between items-center p-4 border-t">
            <div class="flex items-center gap-1.5">
              <span class="text-sm leading-5">表示件数:</span>
              <USelect
                v-model="apiActionLogsPagination.pageCount"
                :options="[10, 20, 30, 50, 100]"
                class="w-20"
              />
              <span class="text-sm text-gray-500 ml-4">
                全 {{ apiActionLogsTotalCount }} 件中
                {{ apiActionLogsFiltered.length }} 件表示
              </span>
            </div>
            <UPagination
              v-if="apiActionLogsTotalCount > apiActionLogsPagination.pageCount"
              v-model="apiActionLogsPagination.page"
              :page-count="apiActionLogsPagination.pageCount"
              :total="apiActionLogsTotalCount"
              :ui="{
                wrapper: 'flex items-center gap-1',
                rounded: '!rounded-full min-w-[32px] justify-center',
                default: {
                  activeButton: {
                    variant: 'outline'
                  }
                }
              }"
            />
          </div>
        </div>
      </UDashboardPanelContent>

      <!-- Log Details Modal -->
      <UModal
        v-model="isModalOpen"
        :ui="{ width: 'sm:max-w-[90vw]' }"
      >
        <UCard class="max-h-[90vh] overflow-hidden flex flex-col">
          <template #header>
            <div class="flex justify-between items-center">
              <h3 class="text-lg font-semibold">
                API操作ログ詳細
              </h3>
              <UButton
                color="gray"
                variant="ghost"
                icon="i-heroicons-x-mark-20-solid"
                class="-my-1"
                @click="isModalOpen = false"
              />
            </div>
          </template>

          <div
            v-if="selectedLogDetail"
            class="space-y-4 overflow-auto flex-1"
          >
            <div class="grid grid-cols-3 gap-4">
              <div>
                <h4 class="font-medium text-sm text-gray-500 mb-1">
                  日時
                </h4>
                <p>
                  {{ new Date(selectedLogDetail.created_at).toLocaleString() }}
                </p>
              </div>
              <div>
                <h4 class="font-medium text-sm text-gray-500 mb-1">
                  ユーザ名
                </h4>
                <p>{{ selectedLogDetail.created_username || "-" }}</p>
              </div>
              <div>
                <h4 class="font-medium text-sm text-gray-500 mb-1">
                  ユーザID
                </h4>
                <p>{{ selectedLogDetail.user_id }}</p>
              </div>
              <div>
                <h4 class="font-medium text-sm text-gray-500 mb-1">
                  アクション
                </h4>
                <p>{{ selectedLogDetail.action }}</p>
              </div>
              <div>
                <h4 class="font-medium text-sm text-gray-500 mb-1">
                  テーブル名
                </h4>
                <p>{{ selectedLogDetail.table_name || "-" }}</p>
              </div>
              <div>
                <h4 class="font-medium text-sm text-gray-500 mb-1">
                  エンドポイント
                </h4>
                <p>{{ selectedLogDetail.endpoint }}</p>
              </div>
              <div>
                <h4 class="font-medium text-sm text-gray-500 mb-1">
                  テナントID
                </h4>
                <p>{{ selectedLogDetail.tenant_id }}</p>
              </div>
              <div>
                <h4 class="font-medium text-sm text-gray-500 mb-1">
                  環境ID
                </h4>
                <p>{{ selectedLogDetail.env_id || "-" }}</p>
              </div>
              <div>
                <h4 class="font-medium text-sm text-gray-500 mb-1">
                  リクエストメソッド
                </h4>
                <p>{{ selectedLogDetail.method }}</p>
              </div>
            </div>

            <div
              v-if="
                selectedLogDetail.original_data || selectedLogDetail.new_data
              "
              class="mt-4"
            >
              <div class="flex justify-between items-center mb-2">
                <h4 class="font-medium text-sm text-gray-500">
                  データ変更
                </h4>
                <div class="flex items-center gap-2">
                  <UToggle v-model="showDiffView" />
                  <span class="text-xs text-gray-500">差分表示</span>
                </div>
              </div>

              <!-- Diff View -->
              <div
                v-if="
                  showDiffView
                    && selectedLogDetail.original_data
                    && selectedLogDetail.new_data
                "
              >
                <UCard class="bg-gray-50 dark:bg-gray-800 p-2">
                  <div
                    v-for="(diff, index) in getDiffData()"
                    :key="index"
                    class="mb-2 pb-2 border-b border-gray-200 dark:border-gray-700 last:border-0"
                  >
                    <div class="flex items-center gap-2 mb-1">
                      <UBadge
                        :color="
                          diff.status === 'added'
                            ? 'green'
                            : diff.status === 'removed'
                              ? 'red'
                              : 'orange'
                        "
                        size="xs"
                      >
                        {{
                          diff.status === "added"
                            ? "追加"
                            : diff.status === "removed"
                              ? "削除"
                              : "変更"
                        }}
                      </UBadge>
                      <span class="font-mono text-xs font-semibold">{{
                        diff.key
                      }}</span>
                    </div>

                    <div
                      v-if="diff.status === 'modified'"
                      class="grid grid-cols-2 gap-4"
                    >
                      <div class="bg-red-50 dark:bg-red-900/20 p-3 rounded">
                        <pre class="text-sm whitespace-pre-wrap break-all">{{
                          JSON.stringify(diff.originalValue, null, 2)
                        }}</pre>
                      </div>
                      <div class="bg-green-50 dark:bg-green-900/20 p-3 rounded">
                        <pre class="text-sm whitespace-pre-wrap break-all">{{
                          JSON.stringify(diff.newValue, null, 2)
                        }}</pre>
                      </div>
                    </div>

                    <div
                      v-else-if="diff.status === 'added'"
                      class="bg-green-50 dark:bg-green-900/20 p-3 rounded"
                    >
                      <pre class="text-sm whitespace-pre-wrap break-all">{{
                        JSON.stringify(diff.newValue, null, 2)
                      }}</pre>
                    </div>

                    <div
                      v-else-if="diff.status === 'removed'"
                      class="bg-red-50 dark:bg-red-900/20 p-3 rounded"
                    >
                      <pre class="text-sm whitespace-pre-wrap break-all">{{
                        JSON.stringify(diff.originalValue, null, 2)
                      }}</pre>
                    </div>
                  </div>

                  <div
                    v-if="!getDiffData() || getDiffData().length === 0"
                    class="text-center text-gray-500 py-4"
                  >
                    変更はありません
                  </div>
                </UCard>
              </div>

              <!-- Original View -->
              <div
                v-else
                class="grid grid-cols-1 md:grid-cols-2 gap-4"
              >
                <div v-if="selectedLogDetail.original_data">
                  <h4 class="font-medium text-sm text-gray-500 mb-1">
                    元データ
                  </h4>
                  <UCard class="bg-gray-50 dark:bg-gray-800 p-3">
                    <pre class="text-sm whitespace-pre-wrap break-all">{{
                      JSON.stringify(
                        safeParseJSON(selectedLogDetail.original_data),
                        null,
                        2
                      )
                    }}</pre>
                  </UCard>
                </div>

                <div v-if="selectedLogDetail.new_data">
                  <h4 class="font-medium text-sm text-gray-500 mb-1">
                    新データ
                  </h4>
                  <UCard class="bg-gray-50 dark:bg-gray-800 p-3">
                    <pre class="text-sm whitespace-pre-wrap break-all">{{
                      JSON.stringify(
                        safeParseJSON(selectedLogDetail.new_data),
                        null,
                        2
                      )
                    }}</pre>
                  </UCard>
                </div>
              </div>
            </div>
          </div>

          <template #footer>
            <div class="flex justify-end">
              <UButton @click="isModalOpen = false">
                閉じる
              </UButton>
            </div>
          </template>
        </UCard>
      </UModal>
    </UDashboardPanel>
  </UDashboardPage>
</template>
