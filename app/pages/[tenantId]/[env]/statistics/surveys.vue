<script setup lang="ts">
const reportsStore = useReportsStore()
const ragsStore = useRagsStore()
const settingsSurveyStore = useSettingsSurveyStore()
const { selectedTenantId, selectedEnvId } = useApp()
const {
  range,
  surveyFilters,
  surveyReportsData,
  loadings,
  surveyReportsPagination,
  selectedSurveyReportsData,
  surveyReportsDataTotal
} = storeToRefs(reportsStore)
const route = useRoute()
const { settingsSurveyOptions } = storeToRefs(settingsSurveyStore)
onMounted(async () => {
  await ragsStore.ragTenantLogin()
  await settingsSurveyStore.fetchAllSettingsSurveyOptions(
    selectedTenantId.value,
    selectedEnvId.value
  )
  if (!route.query.tour) {
    reportsStore.getSurveyReport()
  }
})

const refresh = async () => {
  await reportsStore.getSurveyReport()
}
watch(
  () => range.value,
  async () => {
    refresh()
    selectedSurveyReportsData.value = []
  }
)
watch(
  () => surveyFilters.value.score,
  async () => {
    await refresh()
    selectedSurveyReportsData.value = []
  }
)
watch(
  () => surveyReportsPagination.value.pageCount,
  () => {
    surveyReportsPagination.value.page = 1
  }
)
watch(
  () => surveyReportsPagination.value,
  () => {
    refresh()
  },
  { deep: true }
)

// Watch for sort changes and update pagination asc property
const sort = ref({
  column: 'survey_date',
  direction: 'desc' as 'asc' | 'desc'
})
watch(
  () => sort.value,
  (newSort) => {
    // Update the asc property based on sort direction
    surveyReportsPagination.value.asc = newSort.direction === 'asc'
    // Reset to page 1 when sorting changes
    surveyReportsPagination.value.page = 1
  },
  { deep: true }
)
const defaultColumns = [
  {
    key: 'session_id',
    label: 'ID',
    sortable: false
  },
  {
    key: 'query',
    label: '質問',
    sortable: false
  },
  {
    key: 'answer',
    label: '回答',
    sortable: false
  },
  {
    key: 'score_text_when_recorded',
    label: '選択肢',
    sortable: false
  },
  {
    key: 'survey_date',
    label: 'アンケート回答日時',
    sortable: true
  },
  {
    key: 'action'
  }
]
const scoreOptions = computed(() =>
  settingsSurveyOptions.value?.length
    ? settingsSurveyOptions.value.map(option => ({
        label: option.text,
        value: option.value
      }))
    : []
)

const items = [
  [
    {
      label: 'CSV出力',
      icon: 'ph:file-csv-light',
      click: () =>
        exportUnansweredQuestionsToCSV(
          selectedSurveyReportsData.value,
          'survey-report-data'
        )
    }
  ]
]
const rowMenus = (row: any) => {
  return [
    [
      {
        label: 'CSV出力',
        icon: 'ph:file-csv-light',
        click: () => exportUnansweredQuestionsToCSV([row], row?.query)
      }
    ]
  ]
}
const exportUnansweredQuestionsToCSV = (rows: any[], documentName?: string) => {
  reportsStore.exportUnansweredQuestionsToCSV(rows, documentName)
}
</script>

<template>
  <UDashboardPage>
    <UDashboardPanel grow>
      <UDashboardNavbar
        data-tour="surveys-navbar"
        title="回答後のアンケート"
        :badge="surveyReportsDataTotal"
      />

      <UDashboardToolbar>
        <template #left>
          <div class="flex items-center gap-1.5">
            <BaseDateRangePicker
              v-model="range"
              data-tour="surveys-date-range-picker"
              :exclude-today="true"
            />
            <USelectMenu
              v-if="settingsSurveyOptions?.length"
              v-model="surveyFilters.score"
              data-tour="surveys-score-filter"
              icon="i-heroicons-check-circle"
              placeholder="評価"
              class="w-40"
              :options="scoreOptions"
              :ui-menu="{ option: { base: 'capitalize' } }"
            >
              <template #label>
                <div v-if="surveyFilters.score">
                  {{ surveyFilters.score.label }}
                </div>
                <div v-else>
                  評価
                </div>
              </template>

              <template
                v-if="surveyFilters.score"
                #trailing
              >
                <UButton
                  size="xs"
                  icon="i-lucide-delete"
                  color="gray"
                  :class="[
                    'ml-2 px-2 py-1 rounded hover:text-red-600 !pointer-events-auto'
                  ]"
                  @click.stop="
                    () => {
                      surveyFilters.score = null;
                    }
                  "
                />
              </template>
            </USelectMenu>
          </div>
        </template>
        <template #right>
          <div class="flex items-center gap-1.5">
            <div />
            <div v-if="selectedSurveyReportsData.length">
              <UDropdown
                data-tour="surveys-bulk-actions"
                :items="items"
                :popper="{ placement: 'bottom-start' }"
              >
                <UButton
                  color="white"
                  :label="`一括操作（${selectedSurveyReportsData.length}件）`"
                  icon="fluent:form-multiple-20-regular"
                  trailing-icon="i-heroicons-chevron-down-20-solid"
                  size="sm"
                />
              </UDropdown>
            </div>
            <UButton
              data-tour="surveys-refresh-button"
              icon="prime:sync"
              color="gray"
              size="sm"
              @click="refresh"
            />
          </div>
        </template>
      </UDashboardToolbar>
      <UTable
        v-if="surveyReportsData"
        v-model="selectedSurveyReportsData"
        v-model:sort="sort"
        data-tour="surveys-table"
        :rows="surveyReportsData"
        :columns="defaultColumns"
        :loading="loadings.getSurveyReport"
        :sort-asc-icon="undefined"
        :sort-desc-icon="undefined"
        class="w-full"
        :ui="{
          divide: 'divide-gray-200 dark:divide-gray-800',
          tr: { base: 'group' }
        }"
      >
        <template #session_id-data="{ row }">
          <div class="text-xs flex flex-col">
            <UTooltip :text="row.session_id">
              <div>・セッションID: {{ shorterString(row.session_id) }}</div>
            </UTooltip>
            <UTooltip :text="row.request_id">
              <div>・リクエストID: {{ shorterString(row.request_id) }}</div>
            </UTooltip>
          </div>
        </template>
        <template #query-data="{ row }">
          <div class="whitespace-pre-wrap break-words min-w-32 max-w-1/4">
            {{ row.query }}
          </div>
        </template>
        <template #answer-data="{ row }">
          <div
            class="text-xs whitespace-pre-wrap break-words min-w-72 max-w-1/4"
          >
            {{ row.answer }}
          </div>
        </template>
        <template #score_text_when_recorded-data="{ row }">
          <div class="w-32 whitespace-pre-wrap">
            {{ row.score_text_when_recorded }}
          </div>
        </template>
        <template #survey_date-data="{ row }">
          <div>
            <div>
              {{ formatDateTime(row.survey_date) }}
            </div>
          </div>
        </template>
        <template #action-data="{ row }">
          <UDropdown
            class="group-hover:visible"
            :class="{
              invisible: !route.query.tour
            }"
            :items="rowMenus(row)"
            :popper="{ placement: 'bottom-start' }"
          >
            <UButton
              class="row-menu"
              color="white"
              icon="charm:menu-meatball"
              size="xs"
              square
              :loading="false"
            />
          </UDropdown>
        </template>
      </UTable>
      <UDivider class="mt-0" />
      <UDashboardToolbar>
        <template #left>
          <div class="flex items-center gap-1.5">
            <span class="text-sm leading-5">表示件数:</span>

            <USelect
              v-model="surveyReportsPagination.pageCount"
              :options="[3, 5, 10, 20, 30, 40]"
              class="w-20"
            />
          </div>
        </template>

        <template #right>
          <UPagination
            v-if="surveyReportsPagination.pageCount < surveyReportsDataTotal"
            v-model="surveyReportsPagination.page"
            data-tour="surveys-pagination"
            :page-count="surveyReportsPagination.pageCount"
            :total="surveyReportsDataTotal"
            size="sm"
            :ui="{
              wrapper: 'flex items-center gap-1',
              rounded: '!rounded-full min-w-[32px] justify-center',
              default: {
                activeButton: {
                  variant: 'outline'
                }
              }
            }"
          />
        </template>
      </UDashboardToolbar>
    </UDashboardPanel>
  </UDashboardPage>
</template>
