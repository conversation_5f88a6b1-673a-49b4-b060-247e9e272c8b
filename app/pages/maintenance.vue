<script setup lang="ts">
// Use blank layout
definePageMeta({
  layout: 'blank'
})

// Set page title and meta
useHead({
  title: 'システムメンテナンス中 - PNL',
  meta: [
    {
      name: 'description',
      content: 'システムメンテナンス中です。ご不便をおかけして申し訳ございません。'
    }
  ]
})

// Get maintenance status from store
const maintenanceStore = useMaintenanceStore()
const { getSystemMaintenanceStatus } = storeToRefs(maintenanceStore)

// Reactive maintenance data
const maintenanceData = computed(() => {
  const status = getSystemMaintenanceStatus.value
  return {
    message: status?.message || 'システムメンテナンス中です。しばらくお待ちください。',
    startDate: status?.start_date,
    endDate: status?.end_date,
    isActive: status?.maintenance
  }
})

// Check if user should see this page
const authStore = useAuthStore()
const router = useRouter()

// Redirect operators away from maintenance page
watchEffect(() => {
  if (authStore.user && authStore.isOperator) {
    // Operators should not see maintenance page
    router.push('/')
  }
})

// Auto-refresh maintenance status every 30 seconds
let refreshInterval: NodeJS.Timeout | null = null

onMounted(() => {
  // Initial check
  if (!authStore.isOperator) {
    maintenanceStore.checkSystemMaintenance().catch(() => {
      // Ignore errors on maintenance page
    })
  }

  // Set up auto-refresh
  refreshInterval = setInterval(() => {
    if (!authStore.isOperator) {
      maintenanceStore.checkSystemMaintenance().catch(() => {
        // Ignore errors on maintenance page
      })
    }
  }, 30000) // 30 seconds
})

onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
})

// Handle maintenance completion
watch(() => maintenanceData.value.isActive, (isActive) => {
  if (!isActive && maintenanceData.value.endDate) {
    // Maintenance is completed, redirect after a delay
    setTimeout(() => {
      router.push('/')
    }, 5000) // Wait 5 seconds before redirecting
  }
})
</script>

<template>
  <div>
    <MaintenanceDisplay
      :message="maintenanceData.message"
      :start-date="maintenanceData.startDate"
      :end-date="maintenanceData.endDate"
      :show-countdown="true"
    />
  </div>
</template>

<style scoped>
/* Additional styles if needed */
</style>
