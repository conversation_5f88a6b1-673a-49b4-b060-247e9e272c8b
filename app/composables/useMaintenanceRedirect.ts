/**
 * Composable to handle maintenance mode redirects
 */
export const useMaintenanceRedirect = () => {
  const maintenanceStore = useMaintenanceStore()
  const authStore = useAuthStore()
  const router = useRouter()
  const route = useRoute()

  /**
   * Check if current user should be redirected to maintenance page
   */
  const shouldRedirectToMaintenance = computed(() => {
    // Don't redirect if already on maintenance page
    if (route.path === '/maintenance') {
      return false
    }

    // Don't redirect if on operator login page
    if (route.path === '/auth/operator/login') {
      return false
    }

    // Don't redirect logged-in operators
    if (authStore.user && authStore.isOperator) {
      return false
    }

    // Redirect all other users (including non-authenticated) if system is in maintenance mode
    return maintenanceStore.systemMaintenanceStatus?.maintenance
  })

  /**
   * Check if current user should be redirected away from maintenance page
   */
  const shouldRedirectFromMaintenance = computed(() => {
    // Redirect operators away from maintenance page
    if (authStore.isOperator && route.path === '/maintenance') {
      return true
    }

    // Redirect if maintenance is not active and user is on maintenance page
    if (
      !maintenanceStore.systemMaintenanceStatus?.maintenance
      && route.path === '/maintenance'
    ) {
      return true
    }

    return false
  })

  /**
   * Perform maintenance redirect if needed
   */
  const handleMaintenanceRedirect = () => {
    if (shouldRedirectToMaintenance.value) {
      router.push('/maintenance')
    } else if (shouldRedirectFromMaintenance.value) {
      router.push('/')
    }
  }

  /**
   * Set up automatic maintenance redirect watching
   */
  const setupMaintenanceWatch = () => {
    // Watch for maintenance status changes
    watch(
      () => maintenanceStore.systemMaintenanceStatus?.maintenance,
      () => {
        nextTick(() => {
          handleMaintenanceRedirect()
        })
      }
    )

    // Watch for user role changes
    watch(
      () => authStore.isOperator,
      () => {
        nextTick(() => {
          handleMaintenanceRedirect()
        })
      }
    )

    // Watch for route changes
    watch(
      () => route.path,
      () => {
        nextTick(() => {
          handleMaintenanceRedirect()
        })
      }
    )
  }

  /**
   * Check maintenance status and redirect immediately if needed
   */
  const checkAndRedirect = async () => {
    try {
      const systemStatus = await maintenanceStore.checkSystemMaintenance()

      // Handle redirect after status check
      handleMaintenanceRedirect()
    } catch (error) {
      console.warn('Failed to check maintenance status for redirect:', error)
    }
  }

  return {
    shouldRedirectToMaintenance: readonly(shouldRedirectToMaintenance),
    shouldRedirectFromMaintenance: readonly(shouldRedirectFromMaintenance),
    handleMaintenanceRedirect,
    setupMaintenanceWatch,
    checkAndRedirect
  }
}
