// Prompt types enum
export enum PromptType {
  BASIC_QUERY = 1,
  RAG_QUERY = 2,
  ANALYZE = 3,
  REFINE = 4,
  INPUT_CHECKER = 5,
  REPLY_ANALYZE = 6,
  REFERENCE_URL = 10,
  CONTEXT_USEFUL = 11,
  WEATHER_QUERY = 101,
  QUERY_TRANSLATE = 121,
  ANSWER_TRANSLATE = 122
}

// Prompt type labels mapping
export const PROMPT_TYPE_LABELS: Record<number, string> = {
  1: 'Basic Query',
  2: 'RAG Query',
  3: 'Analyze',
  4: 'Refine',
  5: 'Input Checker',
  6: 'Reply Analyze',
  10: 'Reference URL',
  11: 'Context Useful',
  101: 'Weather Query',
  121: 'Query Translate',
  122: 'Answer Translate'
}

// Prompt type icons mapping
export const PROMPT_TYPE_ICONS: Record<number, string> = {
  1: 'i-heroicons-chat-bubble-left-right',
  2: 'i-heroicons-document-magnifying-glass',
  3: 'i-heroicons-chart-bar-square',
  4: 'i-heroicons-pencil-square',
  5: 'i-heroicons-shield-check',
  6: 'i-heroicons-eye',
  10: 'i-heroicons-link',
  11: 'i-heroicons-light-bulb',
  101: 'fluent:weather-partly-cloudy-day-16-regular',
  121: 'i-heroicons-language',
  122: 'i-heroicons-arrow-path-rounded-square'
}
