<template>
  <UDashboardCard
    ref="cardRef"
    :ui="{ body: { padding: '!pb-3 !px-0' } as any }"
  >
    <template #header>
      <div class="flex items-center justify-between w-full">
        <div>
          <p class="text-sm text-gray-500 dark:text-gray-400 font-medium mb-1">
            テナント使用量比較
          </p>
          <p class="text-lg text-gray-900 dark:text-white font-semibold">
            {{ tenantCount }}テナント
          </p>
        </div>
        <div class="flex items-center space-x-2">
          <UButton
            variant="ghost"
            size="sm"
            icon="i-heroicons-chart-bar"
            :color="chartType === 'bar' ? 'primary' : 'gray'"
            @click="chartType = 'bar'"
          />
          <UButton
            variant="ghost"
            size="sm"
            icon="i-heroicons-chart-pie"
            :color="chartType === 'pie' ? 'primary' : 'gray'"
            @click="chartType = 'pie'"
          />
        </div>
      </div>
    </template>

    <div v-if="!hasData" class="flex items-center justify-center h-96 text-gray-500">
      比較するテナントデータがありません
    </div>

    <!-- Bar Chart -->
    <VisXYContainer
      v-else-if="chartType === 'bar'"
      :data="chartData"
      :padding="{ top: 10, bottom: 50, left: 60, right: 20 }"
      class="h-96"
      :width="width"
    >
      <VisGroupedBar
        :x="x"
        :y="y"
        :color="color"
      />

      <VisAxis
        type="x"
        :x="x"
        :tick-format="xTicks"
        :tick-text-angle="45"
      />

      <VisAxis
        type="y"
        :y="y"
      />

      <VisCrosshair
        :color="color"
        :template="template"
      />

      <VisTooltip />
    </VisXYContainer>

    <!-- Pie Chart -->
    <div v-else-if="chartType === 'pie'" class="h-96 flex">
      <div class="grid grid-cols-2 lg:grid-cols-3 gap-4 w-full p-4">
        <div
          v-for="(metric, index) in pieChartMetrics"
          :key="metric.name"
          class="flex flex-col items-center"
        >
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ metric.name }}
          </h4>
          <VisSingleContainer
            :data="metric.data"
            class="h-32 w-32"
            height="128px"
            width="128px"
          >
            <VisDonut
              :value="pieValue"
              :show-empty-segments="false"
              :pad-angle="0.02"
              :arc-width="12"
              :color="(d, i) => metric.colors[i]"
              :central-label="metric.total.toString()"
            />
            <VisTooltip :triggers="pieTooltipTriggers" />
          </VisSingleContainer>
          <div class="text-xs text-center mt-2 space-y-1">
            <div
              v-for="(tenant, tenantIndex) in tenantNames"
              :key="tenant"
              class="flex items-center justify-center space-x-1"
            >
              <div
                class="w-2 h-2 rounded-full"
                :style="{ backgroundColor: metric.colors[tenantIndex] }"
              />
              <span class="text-gray-600 dark:text-gray-400 truncate max-w-16">
                {{ tenant }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </UDashboardCard>
</template>

<script setup lang="ts">
import {
  VisXYContainer,
  VisGroupedBar,
  VisAxis,
  VisCrosshair,
  VisTooltip,
  VisSingleContainer,
  VisDonut
} from '@unovis/vue'
import { Donut } from '@unovis/ts'
import type { TenantUsageData } from '~/stores/usage'

const props = defineProps({
  tenants: {
    type: Array as PropType<TenantUsageData[]>,
    required: true
  }
})

const cardRef = ref<HTMLElement | null>(null)
const chartType = ref<'bar' | 'pie'>('bar')

const { width } = useElementSize(cardRef)

// Computed properties
const hasData = computed(() => {
  return props.tenants.some(tenant => tenant.data !== null)
})

const tenantCount = computed(() => {
  return props.tenants.filter(tenant => tenant.data !== null).length
})

const tenantNames = computed(() => {
  return props.tenants
    .filter(tenant => tenant.data !== null)
    .map(tenant => tenant.tenantName)
})

// Bar chart data
const chartData = computed(() => {
  const validTenants = props.tenants.filter(tenant => tenant.data !== null)
  
  return validTenants.map(tenant => ({
    tenant: tenant.tenantName,
    azureSearch: tenant.data?.usage.azure_search.total || 0,
    chat: tenant.data?.usage.chat.total || 0,
    batch: tenant.data?.usage.batch.total || 0,
    googleSearch: tenant.data?.usage.google_search.total || 0,
    knowledge: tenant.data?.usage.knowledge.total || 0
  }))
})

// Bar chart accessors
const x = (d: any) => d.tenant
const y = [
  (d: any) => d.azureSearch,
  (d: any) => d.chat,
  (d: any) => d.batch,
  (d: any) => d.googleSearch,
  (d: any) => d.knowledge
]

const colors = ['#3B82F6', '#9333EA', '#22C55E', '#F97316', '#6366F1']
const color = (d: any, i: number) => colors[i % colors.length]

const xTicks = (d: any) => d.length > 10 ? d.substring(0, 10) + '...' : d

const template = (d: any) => {
  const metrics = ['Azure Search', 'Chat', 'Batch', 'Google Search', 'Knowledge']
  const values = [d.azureSearch, d.chat, d.batch, d.googleSearch, d.knowledge]
  
  return `
    <div class="text-xs p-2">
      <div class="font-medium mb-1">${d.tenant}</div>
      ${metrics.map((metric, i) => 
        `<div class="flex justify-between">
          <span style="color: ${colors[i]}">${metric}:</span>
          <span class="ml-2 font-medium">${values[i]}</span>
        </div>`
      ).join('')}
    </div>
  `
}

// Pie chart data
const pieChartMetrics = computed(() => {
  const validTenants = props.tenants.filter(tenant => tenant.data !== null)
  
  if (validTenants.length === 0) return []
  
  const metrics = [
    { name: 'Azure Search', key: 'azure_search' },
    { name: 'Chat', key: 'chat' },
    { name: 'Batch', key: 'batch' },
    { name: 'Google Search', key: 'google_search' },
    { name: 'Knowledge', key: 'knowledge' }
  ]
  
  return metrics.map((metric, metricIndex) => {
    const data = validTenants.map(tenant => 
      tenant.data?.usage[metric.key]?.total || 0
    )
    
    const total = data.reduce((sum, value) => sum + value, 0)
    
    return {
      name: metric.name,
      data,
      total,
      colors: validTenants.map((_, i) => colors[i % colors.length])
    }
  })
})

const pieValue = (d: number) => d
const pieTooltipTriggers = {
  [Donut.selectors.segment]: ({ data, index }) => {
    const tenantName = tenantNames.value[index]
    return `<div class="text-xs">${tenantName}: ${data}</div>`
  }
}
</script>
