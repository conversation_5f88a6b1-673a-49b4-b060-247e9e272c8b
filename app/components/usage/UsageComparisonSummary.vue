<template>
  <UCard>
    <template #header>
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold">比較サマリー</h3>
        <div class="flex items-center space-x-2">
          <UButton
            variant="ghost"
            size="sm"
            icon="i-heroicons-arrow-down-tray"
            @click="$emit('export-csv')"
          >
            CSV出力
          </UButton>
          <UButton
            variant="ghost"
            size="sm"
            icon="i-heroicons-x-mark"
            @click="$emit('clear-all')"
          >
            全削除
          </UButton>
        </div>
      </div>
    </template>

    <div v-if="!summary" class="text-center py-8 text-gray-500">
      比較するテナントを追加してください
    </div>

    <div v-else class="space-y-6">
      <!-- Overview Stats -->
      <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
        <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg text-center">
          <div class="text-sm text-blue-600 dark:text-blue-400 font-medium">Azure Search</div>
          <div class="text-xl font-bold text-blue-700 dark:text-blue-300">
            {{ formatNumber(summary.totalAzureSearch) }}
          </div>
          <div class="text-xs text-blue-500 dark:text-blue-400">
            平均: {{ formatNumber(summary.averageAzureSearch) }}
          </div>
        </div>
        
        <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg text-center">
          <div class="text-sm text-purple-600 dark:text-purple-400 font-medium">Chat</div>
          <div class="text-xl font-bold text-purple-700 dark:text-purple-300">
            {{ formatNumber(summary.totalChat) }}
          </div>
          <div class="text-xs text-purple-500 dark:text-purple-400">
            平均: {{ formatNumber(summary.averageChat) }}
          </div>
        </div>
        
        <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg text-center">
          <div class="text-sm text-green-600 dark:text-green-400 font-medium">Batch</div>
          <div class="text-xl font-bold text-green-700 dark:text-green-300">
            {{ formatNumber(summary.totalBatch) }}
          </div>
          <div class="text-xs text-green-500 dark:text-green-400">
            平均: {{ formatNumber(summary.averageBatch) }}
          </div>
        </div>
        
        <div class="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg text-center">
          <div class="text-sm text-orange-600 dark:text-orange-400 font-medium">Google Search</div>
          <div class="text-xl font-bold text-orange-700 dark:text-orange-300">
            {{ formatNumber(summary.totalGoogleSearch) }}
          </div>
          <div class="text-xs text-orange-500 dark:text-orange-400">
            平均: {{ formatNumber(summary.averageGoogleSearch) }}
          </div>
        </div>
        
        <div class="bg-indigo-50 dark:bg-indigo-900/20 p-4 rounded-lg text-center">
          <div class="text-sm text-indigo-600 dark:text-indigo-400 font-medium">Knowledge</div>
          <div class="text-xl font-bold text-indigo-700 dark:text-indigo-300">
            {{ formatNumber(summary.totalKnowledge) }}
          </div>
          <div class="text-xs text-indigo-500 dark:text-indigo-400">
            平均: {{ formatNumber(summary.averageKnowledge) }}
          </div>
        </div>
      </div>

      <!-- Tenant List -->
      <div class="space-y-3">
        <h4 class="text-md font-medium text-gray-700 dark:text-gray-300">
          比較中のテナント ({{ summary.totalTenants }}件)
        </h4>
        
        <div class="space-y-2">
          <div
            v-for="tenant in tenants.filter(t => t.data !== null)"
            :key="tenant.tenantId"
            class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
          >
            <div class="flex items-center space-x-3">
              <div class="w-3 h-3 rounded-full bg-primary-500" />
              <div>
                <div class="font-medium text-gray-900 dark:text-white">
                  {{ tenant.tenantName }}
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">
                  {{ tenant.tenantId }}
                </div>
              </div>
            </div>
            
            <div class="flex items-center space-x-4">
              <div class="text-right">
                <div class="text-sm font-medium text-gray-900 dark:text-white">
                  総計: {{ getTenantTotal(tenant) }}
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  {{ tenant.data?.from_date }} - {{ tenant.data?.to_date }}
                </div>
              </div>
              
              <UButton
                variant="ghost"
                size="sm"
                icon="i-heroicons-x-mark"
                color="red"
                @click="$emit('remove-tenant', tenant.tenantId)"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Ranking -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="space-y-3">
          <h4 class="text-md font-medium text-gray-700 dark:text-gray-300">
            Chat使用量ランキング
          </h4>
          <div class="space-y-2">
            <div
              v-for="(tenant, index) in chatRanking"
              :key="tenant.tenantId"
              class="flex items-center justify-between p-2 rounded"
              :class="index === 0 ? 'bg-yellow-50 dark:bg-yellow-900/20' : 'bg-gray-50 dark:bg-gray-800'"
            >
              <div class="flex items-center space-x-2">
                <span class="text-sm font-bold w-6">{{ index + 1 }}.</span>
                <span class="text-sm">{{ tenant.tenantName }}</span>
              </div>
              <span class="text-sm font-medium">{{ formatNumber(tenant.value) }}</span>
            </div>
          </div>
        </div>

        <div class="space-y-3">
          <h4 class="text-md font-medium text-gray-700 dark:text-gray-300">
            Knowledge使用量ランキング
          </h4>
          <div class="space-y-2">
            <div
              v-for="(tenant, index) in knowledgeRanking"
              :key="tenant.tenantId"
              class="flex items-center justify-between p-2 rounded"
              :class="index === 0 ? 'bg-yellow-50 dark:bg-yellow-900/20' : 'bg-gray-50 dark:bg-gray-800'"
            >
              <div class="flex items-center space-x-2">
                <span class="text-sm font-bold w-6">{{ index + 1 }}.</span>
                <span class="text-sm">{{ tenant.tenantName }}</span>
              </div>
              <span class="text-sm font-medium">{{ formatNumber(tenant.value) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </UCard>
</template>

<script setup lang="ts">
import type { TenantUsageData } from '~/stores/usage'

const props = defineProps({
  tenants: {
    type: Array as PropType<TenantUsageData[]>,
    required: true
  },
  summary: {
    type: Object,
    default: null
  }
})

defineEmits(['export-csv', 'clear-all', 'remove-tenant'])

// Helper functions
const formatNumber = (num: number) => {
  return new Intl.NumberFormat('ja-JP').format(num)
}

const getTenantTotal = (tenant: TenantUsageData) => {
  if (!tenant.data) return 0
  
  const usage = tenant.data.usage
  return usage.azure_search.total + 
         usage.chat.total + 
         usage.batch.total + 
         usage.google_search.total + 
         usage.knowledge.total
}

// Rankings
const chatRanking = computed(() => {
  return props.tenants
    .filter(t => t.data !== null)
    .map(tenant => ({
      tenantId: tenant.tenantId,
      tenantName: tenant.tenantName,
      value: tenant.data?.usage.chat.total || 0
    }))
    .sort((a, b) => b.value - a.value)
})

const knowledgeRanking = computed(() => {
  return props.tenants
    .filter(t => t.data !== null)
    .map(tenant => ({
      tenantId: tenant.tenantId,
      tenantName: tenant.tenantName,
      value: tenant.data?.usage.knowledge.total || 0
    }))
    .sort((a, b) => b.value - a.value)
})
</script>
