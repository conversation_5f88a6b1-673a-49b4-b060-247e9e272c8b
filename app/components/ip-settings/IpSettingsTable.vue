<script setup lang="ts">
import type { IpSetting } from '~/stores/ip-settings'

const props = defineProps({
  ipSettings: {
    type: Array as PropType<IpSetting[]>,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  ipSettingsPagination: {
    type: Object,
    required: true
  },
  ipSettingsTotal: {
    type: Number,
    required: true
  },
  ipSettingsFilter: {
    type: Object,
    required: true
  }
})

const emit = defineEmits([
  'edit',
  'delete',
  'create',
  'refresh',
  'toggleStatus'
])

const { hasPermission } = useAppPermissions()
const { PERMISSIONS } = useAppPermissions()
const route = useRoute()
const confirm = useConfirm()

function getItems(row: IpSetting) {
  return [
    [
      {
        label: 'IP設定編集',
        icon: 'tabler:edit',
        click: () => emit('edit', row),
        disabled: !hasPermission(PERMISSIONS.EDIT_IP_ADDRESS_SETTING)
      }
    ],
    [
      {
        label: row.status ? '無効化' : '有効化',
        icon: 'ri:toggle-line',
        click: () => toggleIpSettingStatus(row),
        disabled: !hasPermission(PERMISSIONS.EDIT_IP_ADDRESS_SETTING)
      },
      {
        label: 'IP設定削除',
        icon: 'i-heroicons-trash-20-solid',
        class: 'text-red-500 dark:text-red-400',
        iconClass: 'text-red-500 dark:text-red-400',
        click: () => emit('delete', row),
        disabled: !hasPermission(PERMISSIONS.DELETE_IP_ADDRESS_SETTING)
      }
    ]
  ]
}

const defaultColumns = [
  {
    key: 'ip_address',
    label: 'IPアドレス',
    sortable: false
  },
  {
    key: 'priority',
    label: '優先度',
    sortable: false
  },

  {
    key: 'created_at',
    label: '作成日時',
    sortable: false
  },
  {
    key: 'updated_at',
    label: '更新日時',
    sortable: false
  },
  {
    key: 'status',
    label: 'ステータス',
    sortable: false
  },
  {
    key: 'actions',
    label: '操作',
    sortable: false,
    class: 'w-fit text-right'
  }
]

// Status filter options
const statusFilterOptions = [
  { label: 'すべて', value: undefined },
  { label: '有効', value: true },
  { label: '無効', value: false }
]

// Priority range inputs
const priorityMin = ref<number | undefined>()
const priorityMax = ref<number | undefined>()

// Watch priority inputs and update filter
watch([priorityMin, priorityMax], ([min, max]) => {
  props.ipSettingsFilter.priority_min = min
  props.ipSettingsFilter.priority_max = max
})

function toggleIpSettingStatus(row: IpSetting) {
  confirm.show({
    title: 'ステータス変更',
    description: `このIPアドレスのステータスを${
      row.status ? '無効' : '有効'
    }に変更しますか？`,
    confirmText: '変更',
    onConfirm: async () => {
      emit('toggleStatus', row)
    }
  })
}
</script>

<template>
  <UCard
    class="w-full"
    :ui="{
      base: '',
      ring: '',
      divide: 'divide-y divide-gray-200 dark:divide-gray-700',
      header: { padding: '!px-3 !py-3' },
      body: {
        padding: '',
        base: 'divide-y divide-gray-200 dark:divide-gray-700'
      },
      footer: { padding: 'p-4' }
    }"
  >
    <template #header>
      <div class="flex items-center justify-between">
        <h2
          class="flex items-center gap-2 font-semibold text-md text-gray-900 dark:text-white leading-tight"
        >
          IPアドレス制御一覧
          <UBadge
            v-if="ipSettingsTotal"
            :label="ipSettingsTotal"
            variant="solid"
            color="gray"
          />
        </h2>
        <UButton
          v-if="hasPermission(PERMISSIONS.CREATE_IP_ADDRESS_SETTING)"
          label="IP制御追加"
          icon="heroicons:plus"
          color="gray"
          size="sm"
          @click="emit('create')"
        />
      </div>
    </template>

    <!-- Filters -->
    <div class="flex flex-col gap-3 px-4 py-3">
      <div class="flex items-center justify-between gap-3">
        <!-- <div class="flex gap-3 items-center flex-1">
          <UInput
            v-model="props.ipSettingsFilter.ip_address"
            icon="i-heroicons-magnifying-glass-20-solid"
            placeholder="IPアドレスで検索（完全一致）"
            class="flex-1 max-w-xs"
            @keydown.esc="$event.target.blur()"
          >
            <template #trailing>
              <UKbd value="/" />
            </template>
          </UInput>

          <USelectMenu
            v-model="props.ipSettingsFilter.status"
            icon="i-heroicons-check-circle"
            placeholder="ステータス"
            class="w-32"
            :options="statusFilterOptions"
            :ui-menu="{ option: { base: 'capitalize' } }"
          >
            <template #label>
              <span v-if="props.ipSettingsFilter.status !== undefined">
                {{ props.ipSettingsFilter.status ? '有効' : '無効' }}
              </span>
              <span v-else>すべて</span>
            </template>
          </USelectMenu>
        </div> -->
        <div />
        <UButton
          icon="prime:sync"
          color="gray"
          size="sm"
          data-tour="ip-address-control-refresh"
          @click="emit('refresh')"
        />
      </div>
    </div>

    <UTable
      v-if="ipSettings"
      :rows="ipSettings"
      :columns="defaultColumns"
      class="w-full"
      :ui="{
        divide: 'divide-gray-200 dark:divide-gray-800',
        tr: { base: 'group' }
      }"
      :loading="loading"
    >
      <template #ip_address-data="{ row }">
        <div class="flex items-center gap-2">
          <UIcon
            name="eos-icons:ip"
            class="text-gray-400"
          />
          <span class="font-mono">{{ row.ip_address }}</span>
        </div>
      </template>

      <template #status-data="{ row }">
        <!-- <UBadge
          :label="row.status ? '有効' : '無効'"
          :color="row.status ? 'green' : 'red'"
          :icon="row.status ? 'heroicons:check-circle' : 'heroicons:x-circle'"
          variant="subtle"
          size="sm"
          :ui="{
            rounded: 'rounded-full'
          }"
        /> -->
        <BaseStatusToggleNoConfirm
          v-model="row.status"
          @toggle="toggleIpSettingStatus(row)"
        />
      </template>

      <template #priority-data="{ row }">
        <div class="flex items-center gap-2">
          <UIcon
            :name="
              row.priority <= 10
                ? 'heroicons:arrow-up'
                : row.priority <= 50
                  ? 'heroicons:minus'
                  : 'heroicons:arrow-down'
            "
            :class="
              row.priority <= 10
                ? 'text-red-500'
                : row.priority <= 50
                  ? 'text-yellow-500'
                  : 'text-green-500'
            "
          />
          <span class="font-semibold">{{ row.priority }}</span>
        </div>
      </template>

      <template #created_at-data="{ row }">
        <div>
          <div class="text-sm">
            {{ formatDateTime(row.created_at) }}
          </div>
          <div class="text-xs text-gray-500 dark:text-gray-400">
            {{ row.created_username }}
          </div>
        </div>
      </template>

      <template #updated_at-data="{ row }">
        <div>
          <div class="text-sm">
            {{ formatDateTime(row.updated_at) }}
          </div>
          <div class="text-xs text-gray-500 dark:text-gray-400">
            {{ row.updated_username }}
          </div>
        </div>
      </template>

      <template #actions-data="{ row }">
        <div class="flex flex-row items-end justify-end">
          <UDropdown
            class=" group-hover:block"
            :items="getItems(row)"
            :popper="{ placement: 'bottom-start' }"
            :class="{
              hidden: !route.query.tour
            }"
          >
            <UButton
              class="row-menu"
              color="white"
              icon="charm:menu-meatball"
              size="xs"
              square
            />
          </UDropdown>
        </div>
      </template>
    </UTable>

    <!-- Number of rows & Pagination -->
    <template #footer>
      <div
        class="flex flex-wrap justify-between items-center"
        data-tour="ip-address-footer"
      >
        <div class="flex items-center gap-1.5">
          <span class="text-sm leading-5">表示件数:</span>
          <USelect
            v-model="ipSettingsPagination.pageCount"
            :options="[5, 10, 20, 30, 50]"
            class="w-20"
          />
        </div>

        <UPagination
          v-model="ipSettingsPagination.page"
          :page-count="ipSettingsPagination.pageCount"
          :total="ipSettingsTotal"
          :ui="{
            wrapper: 'flex items-center gap-1',
            rounded: '!rounded-full min-w-[32px] justify-center',
            default: {
              activeButton: {
                variant: 'outline'
              }
            }
          }"
        />
      </div>
    </template>
  </UCard>
</template>
