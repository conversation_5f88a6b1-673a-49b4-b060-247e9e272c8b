<script setup lang="ts">
import { debounce } from 'lodash'
const route = useRoute()
const model = defineModel<boolean>()
const props = defineProps<{
  username?: string
  userAccessLogs?: any[]
  loadings?: any
  userAccessLogPagination?: any
  usersAccessLogTotalCount?: number
  userAccessLogFilter?: any
  onFetchLogs: () => Promise<boolean>
}>()

const {
  userAccessLogs = ref([]),
  loadings = ref({}),
  userAccessLogPagination = ref({}),
  usersAccessLogTotalCount = ref(0),
  userAccessLogFilter = ref({})
} = toRefs(props)

const expand = ref({
  openedRows: [],
  row: {}
})

watch([model, () => props.username], ([isOpen, username]) => {
  if (isOpen && username) {
    userAccessLogFilter.value.username = username
    props.onFetchLogs()
  }
})

const debouncedSearch = debounce(() => {
  props.onFetchLogs()
}, 500)

watch(model, (isOpen) => {
  if (!isOpen) {
    userAccessLogFilter.value.username = ''
  }
})
watch(
  () => userAccessLogPagination.value.pageCount,
  () => {
    userAccessLogPagination.value.page = 1
  }
)

watch(
  () => userAccessLogPagination.value,
  () => {
    props.onFetchLogs()
  },
  { deep: true }
)
watch(
  () => userAccessLogFilter.value.ip_address,
  () => {
    userAccessLogPagination.value.page = 1
    debouncedSearch()
  }
)
const emit = defineEmits<{
  'release-user': [username: string]
  'release-ip': [ipAddress: string]
}>()

const rowMenus = (row: any) => {
  return [
    [
      {
        label: 'ユーザロック解除',
        icon: 'ph:lock-simple-open-fill',
        click: () => emit('release-user', row.username)
      },
      {
        label: 'IPアドレスロック解除',
        icon: 'ph:lock-simple-open-fill',
        click: () => emit('release-ip', row.ip_address)
      }
    ]
  ]
}

const defaultColumns = [
  {
    key: 'ip_address',
    label: 'IPアドレス',
    sortable: true
  },
  {
    key: 'count_lock',
    label: 'ロック状態',
    sortable: true
  },
  {
    key: 'release_username',
    label: '解除したユーザ',
    sortable: true
  },
  {
    key: 'created_at',
    label: '作成日時',
    sortable: true
  },
  {
    key: 'release_at',
    label: 'ロック解除日時',
    sortable: true
  },
  {
    key: 'id',
    label: '',
    sortable: false
  }
]
</script>

<template>
  <UModal
    v-model="model"
    :prevent-close="true"
    :ui="{ width: 'sm:max-w-[90vw]' }"
  >
    <UCard
      :ui="{
        base: '',
        ring: '',
        divide: 'divide-y divide-gray-200 dark:divide-gray-700',
        header: { padding: '!px-3 !py-3' },
        body: {
          padding: '',
          base: 'divide-y divide-gray-200 dark:divide-gray-700'
        },
        footer: { padding: 'p-4' }
      }"
    >
      <template #header>
        <div class="flex items-center justify-between">
          <h2 class="flex items-center gap-2 font-semibold text-md text-gray-900 dark:text-white leading-tight">
            ユーザー
            <div class="flex items-center gap-1">
              <UAvatar
                v-if="username"
                size="xs"
                :alt="username"
              />
              <div class="text-xs text-gray-500 dark:text-gray-400">
                {{ username }}
              </div>
              のアクセスログ
            </div>
            <UBadge
              v-if="userAccessLogs?.length"
              :label="usersAccessLogTotalCount"
            />
          </h2>
          <UButton
            color="gray"
            variant="ghost"
            icon="i-heroicons-x-mark-20-solid"
            class="-my-1"
            @click="model = false"
          />
        </div>
      </template>

      <div class="flex items-center justify-between gap-3 px-4 py-3">
        <div class="flex items-center gap-3">
          <UInput
            v-model="userAccessLogFilter.ip_address"
            icon="i-heroicons-magnifying-glass-20-solid"
            placeholder="IPアドレス..."
            @keydown.esc="$event.target.blur()"
          />
        </div>
        <div class="flex items-center gap-1.5">
          <UButton
            icon="prime:sync"
            color="gray"
            size="sm"
            @click="props.onFetchLogs()"
          />
        </div>
      </div>

      <UTable
        v-if="userAccessLogs"
        v-model:expand="expand"
        :rows="userAccessLogs"
        :columns="defaultColumns"
        :loading="loadings.getAccessLogs"
        class="flex-1"
        :ui="{
          divide: 'divide-gray-200 dark:divide-gray-800',
          tr: { base: 'group' }
        }"
      >
        <template #expand="{ row }">
          <div class="p-2 text-xs text-gray-500 dark:text-gray-500">
            <pre>{{ row?.user_agent || "" }}</pre>
          </div>
        </template>

        <template #count_lock-data="{ row }">
          <UBadge
            :label="row.count_lock ? 'ロックなし' : 'ロック中'"
            :color="row.count_lock ? 'green': 'red'"
            :icon="row.count_lock ? 'heroicons:lock-open' : 'heroicons:lock-closed'"
            variant="subtle"
            size="sm"
            :ui="{
              rounded: 'rounded-full'
            }"
          />
        </template>
        <template #created_at-data="{ row }">
          <div class="text-xs text-gray-500 dark:text-gray-400">
            {{ formatDateTime(row.created_at) }}
          </div>
        </template>
        <template #release_username-data="{ row }">
          <div class="text-xs text-gray-500 dark:text-gray-400">
            {{ row.release_username || "-" }}
          </div>
        </template>
        <template #release_at-data="{ row }">
          <div class="text-xs text-gray-500 dark:text-gray-400">
            {{ row.release_at ? formatDateTime(row.release_at) : "-" }}
          </div>
        </template>
        <template #id-data="{ row }">
          <div class="flex items-center gap-3 justify-between">
            <UDropdown
              v-if="!row.count_lock"
              data-tour="row-menu"
              :class="route.query.tour ? '' : 'group-hover:visible invisible'"
              :items="rowMenus(row)"
              :popper="{ placement: 'bottom-start' }"
            >
              <UButton
                class="row-menu"
                color="white"
                icon="charm:menu-meatball"
                size="xs"
                square
              />
            </UDropdown>
          </div>
        </template>
      </UTable>
      <template #footer>
        <div class="flex flex-wrap justify-between items-center">
          <div class="flex items-center gap-1.5">
            <span class="text-sm leading-5">表示件数:</span>
            <USelect
              v-model="userAccessLogPagination.pageCount"
              :options="[3, 5, 10, 20, 30, 40]"
              class="w-20"
            />
          </div>

          <UPagination
            v-model="userAccessLogPagination.page"
            :page-count="userAccessLogPagination.pageCount"
            :total="usersAccessLogTotalCount"
            :ui="{
              wrapper: 'flex items-center gap-1',
              rounded: '!rounded-full min-w-[32px] justify-center',
              default: {
                activeButton: {
                  variant: 'outline'
                }
              }
            }"
          />
        </div>
      </template>
    </UCard>
  </UModal>
</template>
