<script setup lang="ts">
const environmentsStore = useEnvironmentsStore()
const { loadings, selectedEnv, selectedEnvId, environmentsDropdownItems }
  = storeToRefs(environmentsStore)

const settingsStore = useSettingsStore()
const { customSettings } = storeToRefs(settingsStore)
</script>

<template>
  <UDropdown
    id="env-dropdown"
    data-tour="env-dropdown"
    :items="[environmentsDropdownItems]"
    :ui="{ width: 'w-60' }"
    class="w-full group"
  >
    <UButton
      color="gray"
      variant="solid"
      :loading="loadings.fetchAllEnvs"
      class="w-full"
    >
      <div
        class="flex flex-row gap-1 items-center justify-between w-full truncate"
      >
        <div class="flex flex-row gap-2 items-center">
          <UIcon
            :name="
              selectedEnv?.environment === 1
                ? 'fluent-emoji:star'
                : 'tdesign:logo-codesandbox'
            "
            class="text-lg"
          />
          <span class="font-semibold truncate">{{
            selectedEnv?.environment === 1 ? "本番環境" : "検証環境"
          }}</span>
        </div>
        <UBadge
          v-if="selectedEnv?.version"
          size="xs"
          :color="
            customSettings[selectedEnv?.tenant_id]?.[selectedEnv?.id]
              ?.color_primary
          "
          variant="subtle"
          :ui="{
            size: { xs: 'text-[11px] px-1 py-0' }
          }"
        >
          {{ `V.${selectedEnv?.version}` }}
        </UBadge>
      </div>
      <UIcon name="mingcute:down-line" />
    </UButton>
    <template
      v-for="env in environmentsDropdownItems"
      #[`env${env.id}`]="{ item }"
      :key="env.uuid"
    >
      <div class="flex items-center gap-2 w-full">
        <UAvatar
          :ui="{
            rounded: 'rounded-sm'
          }"
          :icon="
            item?.environment === 1
              ? 'fluent-emoji:star'
              : 'tdesign:logo-codesandbox'
          "
        />

        <div
          class="flex flex-col gap-0 items-start justify-between w-full truncate"
        >
          <span class="text-gray-700 dark:text-gray-200 truncate text-sm">{{
            item?.label
          }}</span>
          <div class="text-gray-500 dark:text-gray-400 truncate text-xs">
            {{ item?.id }}
          </div>
        </div>
        <UBadge
          v-if="item?.version"
          size="xs"
          :color="
            customSettings[selectedEnv?.tenant_id]?.[item?.id]?.color_primary
          "
          variant="soft"
          :ui="{
            size: { xs: 'text-[11px] px-1 py-0' }
          }"
          class="w-14 text-center justify-center"
        >
          {{ `V. ${item?.version}` }}
        </UBadge>
      </div>
    </template>
  </UDropdown>
</template>
