// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  extends: ['@nuxt/ui-pro'],

  modules: [
    '@nuxt/eslint',
    '@nuxt/fonts',
    '@nuxt/ui',
    '@vueuse/nuxt',
    '@pinia/nuxt',
    'pinia-plugin-persistedstate/nuxt',
    '@nuxtjs/i18n',
    '@nuxt/content',
    'nuxt-permissions',
    'motion-v/nuxt',
    'nuxt-tour'
  ],

  plugins: [{ src: 'plugins/vue-number-animation', ssr: false }],
  ssr: false,
  components: [
    {
      path: '~/components',
      pathPrefix: false
    }
  ],

  devtools: {
    enabled: true
  },

  // Page transitions
  app: {
    head: {
      script: [
        {
          src: '/chatbot.min.js',
          async: true
        }
      ]
    }
  },
  css: [
    '~/assets/style/style.css',
    'vue-virtual-scroller/dist/vue-virtual-scroller.css'
  ],

  ui: {
    safelistColors: [
      'primary',
      'red',
      'orange',
      'purple',
      'yellow',
      'green',
      'sky',
      'indigo',
      'yellow',
      'pink',
      'amber',
      'lime',
      'cyan',
      'teal',
      'violet',
      'fuchsia',
      'rose',
      'emerald',
      'blue',
      'gray',
      'trueGray',
      'warmGray',
      'coolGray',
      'blueGray',
      'customone'
    ]
  },
  runtimeConfig: {
    public: {
      api: {
        adminServiceBaseUrl: process.env.ADMIN_SERVICE_BASE_URL || '',
        authServiceBaseUrl: process.env.AUTH_SERVICE_BASE_URL || '',
        ragServiceBaseUrl: process.env.RAG_SERVICE_BASE_URL || '',
        reportServiceBaseUrl: process.env.REPORT_SERVICE_BASE_URL || '',
        maintenanceServiceBaseUrl: process.env.MAINTENANCE_SERVICE_BASE_URL || 'https://0p02gbetp3.execute-api.ap-northeast-1.amazonaws.com'
      },
      version: {
        commit: process.env.NUXT_COMMIT || '',
        buildDate: process.env.NUXT_BUILD_DATE || ''
      },
      features: {
        ipAddressControl: process.env.FEATURES_IP_ADDRESS_CONTROL || false,
        faqImportMode: process.env.FEATURES_FAQ_IMPORT_MODE || false,
        userPasswordChange: process.env.FEATURES_USER_PASSWORD_CHANGE || false,
        support: process.env.FEATURES_SUPPORT || false
      },
      maintenance: {
        enabled: process.env.MAINTENANCE_ENABLED == 'true' || false
      }
    },
    mockApi: {
      delay: 3000
    }
  },
  build: { transpile: ['vue-number-animation'] },

  routeRules: {
    // Temporary workaround for prerender regression. see https://github.com/nuxt/nuxt/issues/27490
    '/': { prerender: true }
  },

  future: {
    compatibilityVersion: 4
  },

  compatibilityDate: '2024-07-11',

  typescript: {
    strict: false
  },

  eslint: {
    config: {
      stylistic: {
        commaDangle: 'never',
        braceStyle: '1tbs'
      }
    }
  },
  i18n: {
    defaultLocale: 'ja',
    locales: [
      {
        code: 'ja',
        name: '日本語',
        file: 'ja.json'
      }
    ],
    lazy: true,
    strategy: 'no_prefix'
  }
})
